package com.moego.server.payment.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Data
public class SyncOrderPaymentDetail {
    Byte serverPaymentStatus;
    String paymentMethodVendor;
    BigDecimal totalAmount;
    BigDecimal paymentTipsAfterCreate;
    BigDecimal processingFee;
    String paymentStatusReason;
    BigDecimal convenienceFee;

    private Byte stripePaymentMethod;
    private String stripeIntentId;
    private String cardFunding;
    private String cardType;
    private String cardNumber;
    private String expMonth;
    private String expYear;
}
