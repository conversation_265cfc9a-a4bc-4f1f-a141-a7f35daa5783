package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/12/1
 */
@Data
@Builder
public class StripeSubscriptionInfo {
    String id;
    Long currentPeriodEnd;
    Long currentPeriodStart;

    @Schema(description = "cancel at billing end date")
    Boolean cancelAtPeriodEnd;

    String description;
    Long created;
    String status;

    @Schema(description = "subscription create date")
    Long startDate;

    String metadata;
    StripeCouponInfo discountInfo;

    StripeInvoiceInfo upcomingInvoice;
    StripeInvoiceInfo latestInvoice;
}
