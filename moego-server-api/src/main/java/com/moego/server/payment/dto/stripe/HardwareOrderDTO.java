package com.moego.server.payment.dto.stripe;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Data
public class HardwareOrderDTO {
    @Schema(description = "下单成功后, 会返回订单号")
    String orderId;

    @Schema(description = "totalAmount = m2Amount + smartReaderAmount + shippingFee + hardwareTax - hardwareDiscount")
    BigDecimal totalAmount;

    @Schema(description = "m2Amount = m2Quantity * $249")
    BigDecimal m2Amount;

    @Schema(description = "smartReaderAmount = smartReaderQuantity * $59")
    BigDecimal smartReaderAmount;

    // update discount for sale
    @Schema(description = "hardwareDiscount = m2Amount * 10% + smartReaderAmount * 10% or zero")
    BigDecimal hardwareDiscount;

    @Schema(description = "stripe returned value")
    BigDecimal shippingFee;

    @Schema(description = "stripe returned value when previewing order")
    BigDecimal hardwareTax;

    public void resetTotalAmount() {
        totalAmount = m2Amount.add(smartReaderAmount)
                .add(shippingFee)
                .add(hardwareTax)
                .subtract(hardwareDiscount);
    }
}
