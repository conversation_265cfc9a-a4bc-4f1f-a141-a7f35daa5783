package com.moego.server.payment.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@Data
@Builder
public class PlatformCareQueryParams {
    String code;

    @Min(1)
    Long agreementId;

    String agreementRecordUUid;

    @Min(1)
    Long accountId;

    @Min(0)
    Integer status;

    String email;

    @Builder.Default
    Byte deleted = 0;

    @Builder.Default
    Byte ordered = 0;

    LocalDate updateTimeStart;
    LocalDate updateTimeEnd;

    Pagination pagination;
}
