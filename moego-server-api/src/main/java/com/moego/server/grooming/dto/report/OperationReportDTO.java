package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class OperationReportDTO {
    private Long id;
    private Integer businessId;
    private Integer groomingId;
    private Integer groomingServiceId;
    private Integer petId;
    private Integer staffId;
    private String operationName = "";
    private Integer startTime;
    private Integer duration;
    private String comment = "";
    private BigDecimal price;
    private BigDecimal priceRatio;
    private Boolean status;
    private Date createTime;
    private Date updateTime;
    private Long companyId;
}
