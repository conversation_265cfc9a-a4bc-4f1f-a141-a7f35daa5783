package com.moego.server.grooming.dto.printcard;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.offering.v1.ServiceItemType;
import java.util.List;

/**
 * Print card service info
 *
 * @param serviceName
 * @param startTime
 * @param duration
 * @param price
 * @param staffId
 * @param staffName
 * @param enableOperation
 * @param operationList
 * @param serviceItem
 * @param lodgingRoom
 * @param serviceType      1-service, 2-add on
 * @param priceUnit       1-per session, 2-per night, 3-per hour, 4-per day
 */
public record PrintCardServiceInfo(
        String serviceName,
        Integer startTime,
        Integer duration,
        String price,
        Integer staffId,
        String staffName,
        Boolean enableOperation,
        List<PrintCardOperation> operationList,
        @Deprecated ServiceItemEnum serviceItem,
        ServiceItemType careType,
        String lodgingRoom,
        Integer serviceType,
        Integer priceUnit) {}
