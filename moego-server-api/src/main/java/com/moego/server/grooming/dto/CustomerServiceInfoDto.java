package com.moego.server.grooming.dto;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CustomerServiceInfoDto {

    private Integer customerId;
    private Date lastServiceDate;
    private Integer lastServiceStartTime;
    private Integer lastServiceEndTime;
    // 多个staff时，取完成时间最后的那个
    private Integer lastServiceStaffId;
    private Date nextServiceDate;
    private Integer nextServiceStartTime;
    private Integer nextServiceEndTime;
    // 多个staff时，取完成时间最后的那个
    private Integer nextServiceStaffId;
}
