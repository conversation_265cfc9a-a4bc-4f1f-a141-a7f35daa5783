package com.moego.server.grooming.dto.appointment.comment;

import lombok.Data;

@Data
public class TicketComment {
    private Long id;

    private String note;

    private Integer createBy;
    private Long createTime;
    private Integer updateBy;
    private Long updateTime;
    private Long businessId;
    // appointment date
    private String appointmentDate;
    private String appointmentEndDate;

    // operator might be staff or customer
    private String operatorFirstName;
    private String operatorLastName;
    private String operatorAvatar;
    private String operatorColorCode;
}
