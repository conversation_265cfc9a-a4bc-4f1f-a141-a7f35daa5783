package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @see <a href="https://moego.atlassian.net/browse/ERP-7492">ERP-7492</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationGoogleReserveGeoMatchedParams extends NotificationParams {
    private String title = "Your Reserve with Google integration has been successfully completed";
    private String body = "";
    private String type = NotificationEnum.TYPE_SYSTEM_GOOGLE_RESERVE_GEO_MATCHED;
    private Boolean isNotifyBusinessAllStaff = true;

    private Map<String, Object> webPushDto = new HashMap<>();
}
