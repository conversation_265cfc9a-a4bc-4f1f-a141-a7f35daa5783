package com.moego.server.customer.dto;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CustomerPrimaryDto {

    private Integer customerId;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String ownerPhoneNumber;
    private String clientColor;
    private String email;
    private Byte sendAutoEmail;
    private Byte sendAutoMessage;
    private Byte sendAppAutoMessage;
    private Integer preferredFrequencyDay;
    private Byte preferredFrequencyType;
    private CustomerAddressDto address;
    private List<String> tagList;

    private Integer referralSourceId;
    private String referralSourceDesc;
    private Integer preferredGroomerId;
    private Integer[] preferredDay;
    private Integer[] preferredTime;
    private Byte status;
    private Boolean hasPetParentAppAccount;
}
