package com.moego.server.customer.params;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerInfoIdParams {

    private Integer businessId;
    private Integer customerId;
    private Long companyId;

    public CustomerInfoIdParams(Integer businessId, Integer customerId) {
        this.businessId = businessId;
        this.customerId = customerId;
    }
}
