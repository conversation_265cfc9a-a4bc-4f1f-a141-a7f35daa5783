syntax = "proto3";

package moego.models.activity_log.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/activity_log/v1;activitylogpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.activity_log.v1";

// ActivityLog model
message ActivityLogModel {
  // id
  string id = 1;
  // business id
  int64 business_id = 2;
  // operator
  Operator operator = 3;
  // action
  string action = 4;
  // resource
  Resource resource = 5;
  // owner, nullable
  optional Owner owner = 8;
  // timestamp
  google.protobuf.Timestamp time = 9;
  // updated result, usually a JSON string
  optional google.protobuf.Value details = 10;
  // root cause activity log id, nullable
  optional string root_activity_log_id = 12;
  // request id
  optional string request_id = 13;
}

// ActivityLog model simple view
message ActivityLogModelSimpleView {
  // id
  string id = 1;
  // business id
  int64 business_id = 2;
  // operator
  Operator operator = 3;
  // action
  string action = 4;
  // resource
  Resource resource = 5;
  // owner, nullable
  optional Owner owner = 7;
  // timestamp
  google.protobuf.Timestamp time = 8;
  // root cause activity log id, nullable
  optional string root_activity_log_id = 9;
}

// Operator
message Operator {
  // operator id
  string id = 1;
  // operator name
  string name = 2;
}

// Owner
message Owner {
  // owner id
  string id = 1;
  // owner name
  string name = 2;
}

// Resource
message Resource {
  // resource id
  string id = 1;
  // resource name
  string name = 2;
  // resource type
  string type = 3;

  // Resource type
  enum Type {
    // Unspecified
    TYPE_UNSPECIFIED = 0;

    // Appointment
    APPOINTMENT = 1;
    // Repeat rule
    REPEAT_RULE = 2;
    // Block
    BLOCK = 3;
    // Grooming service
    GROOMING_SERVICE = 4;
    // Grooming note
    GROOMING_NOTE = 5;

    // Online booking
    LANDING_PAGE_SETTING = 6;
    // Online booking setting, aka. Book Online
    ONLINE_BOOKING_SETTING = 7;
    // Online booking team schedule
    ONLINE_BOOKING_TEAM_SCHEDULE = 8;
    // Online booking service
    ONLINE_BOOKING_SERVICE = 9;
    // Online booking pet limit
    ONLINE_BOOKING_PET_LIMIT = 10;
    // Online booking customized payment
    ONLINE_BOOKING_CUSTOMIZED_PAYMENT = 11;
    // Online booking notification
    ONLINE_BOOKING_NOTIFICATION = 12;
    // Online booking question
    ONLINE_BOOKING_QUESTION = 13;
    // Abandoned booking
    ABANDONED_BOOKING = 14;
    // Customer profile request
    CUSTOMER_PROFILE_REQUEST = 15;

    // Customer
    CUSTOMER = 16;
    // Customer tag
    CUSTOMER_TAG = 17;
    // Customer address
    CUSTOMER_ADDRESS = 18;
    // Customer contact
    CUSTOMER_CONTACT = 19;
    // Customer note
    CUSTOMER_NOTE = 20;
    // Customer preferred tip
    CUSTOMER_PREFERRED_TIP = 21;
    // Pet
    PET = 22;
    // Pet note
    PET_NOTE = 23;
    // Pet behavior
    PET_BEHAVIOR = 24;
    // Pet breed
    PET_BREED = 25;
    // Pet code
    PET_CODE = 26;
    // Pet fixed
    PET_FIXED = 27;
    // Pet photo
    PET_PHOTO = 28;
    // Pet type
    PET_TYPE = 29;
    // Pet vaccine
    PET_VACCINE = 30;
    // Pet vaccine binding
    PET_VACCINE_BINDING = 31;
    // Pet hair length
    PET_HAIR_LENGTH = 32;
    // Customized service
    PET_CUSTOMIZED_SERVICE = 33;
    // Agreement signature
    AGREEMENT_SIGNATURE = 34;
    // pet incident report
    PET_INCIDENT_REPORT = 65;

    // Account
    ACCOUNT = 35;

    // Message
    MESSAGE = 36;
    // Chat
    CHAT = 37;
    // Auto message setting
    AUTO_MESSAGE_SETTING = 38;
    // Reminder setting
    REMINDER_SETTING = 39;
    // Auto reply setting
    AUTO_REPLY_SETTING = 40;
    // Schedule message
    SCHEDULE_MESSAGE = 41;
    // Message template
    MESSAGE_TEMPLATE = 42;

    // Review booster
    REVIEW_BOOSTER = 43;

    // Order
    ORDER = 44;
    // Package
    PACKAGE = 45;
    // Customer package
    CUSTOMER_PACKAGE = 46;
    // Payment
    PAYMENT = 47;
    // Refund
    REFUND = 48;
    // Stripe account
    PAYMENT_STRIPE_ACCOUNT = 49;
    // Payout
    PAYOUT = 50;
    // Terminal
    TERMINAL = 51;
    // Terminal location
    TERMINAL_LOCATION = 52;
    // Stripe card
    STRIPE_CARD = 53;
    // Stripe customer
    STRIPE_CUSTOMER = 54;
    // Square card
    SQUARE_CARD = 55;
    // Square customer
    SQUARE_CUSTOMER = 56;
    // Subscription
    SUBSCRIPTION = 57;
    // Email package
    MESSAGE_EMAIL_PACKAGE = 58;
    // Hardware
    HARDWARE = 59;
    // Customized payment setting
    CUSTOMIZED_PAYMENT_SETTING = 60;
    // Company plan feature
    COMPANY_PLAN_FEATURE = 61;
    // Platform care
    PLATFORM_CARE = 62;

    // Staff
    STAFF = 63;
    // Business setting
    BUSINESS_SETTING = 64;

    // Service category
    SERVICE_CATEGORY = 66;
  }
}
