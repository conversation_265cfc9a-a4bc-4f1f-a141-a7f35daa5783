syntax = "proto3";

package moego.models.payment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/online_booking/v1/ob_config_enums.proto";
import "moego/models/payment/v1/deposit_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// online booking deposit model
message BookOnlineDepositModel {
  // deposit id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // appointment id
  int64 grooming_id = 3;
  // guid
  string guid = 4;
  // payment id
  int64 payment_id = 5;
  // amount
  double amount = 6;
  // booking fee
  double booking_fee = 7;
  // tips amount
  double tips_amount = 8;
  // convenience fee
  double convenience_fee = 9;
  // status
  DepositStatus status = 10;
  // create time
  google.protobuf.Timestamp create_time = 11;
  // update time
  google.protobuf.Timestamp update_time = 12;
  // service total
  double service_total = 13;
  // tax amount
  double tax_amount = 14;
  // service charge amount
  double service_charge_amount = 15;
  // book online payment
  models.online_booking.v1.PaymentType payment_type = 16;
  // prepay type
  models.online_booking.v1.PrepayType prepay_type = 17;
  // prepay deposit amount
  double prepay_deposit_amount = 18;
  // prepay paid amount
  double prepay_paid_amount = 19;
  // discount amount
  double discount_amount = 20;
}

// ob deposit model in c app view
message BookOnlineDepositModelClientView {
  // service total
  double service_total = 1;
  // tax amount
  double tax_amount = 2;
  // tips amount
  double tips_amount = 3;
  // service charge amount
  double service_charge_amount = 4;
  // booking fee
  double booking_fee = 5;
  // convenience fee
  double convenience_fee = 6;
  // amount
  double amount = 7;
  // book online payment
  models.online_booking.v1.PaymentType payment_type = 8;
  // prepay type
  models.online_booking.v1.PrepayType prepay_type = 9;
  // prepay deposit amount
  double prepay_deposit_amount = 10;
  // prepay paid amount
  double prepay_paid_amount = 11;
  // discount amount
  double discount_amount = 12;
}
