syntax = "proto3";

package moego.models.payment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// Pre-auth enable definition
// Can pass the association when creating an appointment,
// or can send a request cof link to associate at the time of callback.
message PreAuthEnableDef {
  // Enable pre-auth
  bool enable = 1 [(validate.rules).bool = {const: true}];
  // Stripe payment method id, such as pm_1ObH0nIZwcIFVLGrR5SnOkLU
  optional string payment_method_id = 2 [(validate.rules).string = {
    pattern: "^pm_.*$"
    max_len: 255
  }];
  // Card brand and last 4 digits, such as Visa(1111)
  optional string card_brand_last4 = 3 [(validate.rules).string = {max_len: 255}];
}
