syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The boarding add-on detail
message BoardingAddOnDetailModel {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of boarding service detail, associated with the current add-on
  int64 service_detail_id = 3;
  // The id of pet, associated with the current add-on
  int64 pet_id = 4;
  // The id of current add-on service
  int64 add_on_id = 5;
  // The specific dates of the add-on service
  repeated string specific_dates = 6;
  // whether the add-on service is everyday, not include checkout day
  // deprecated. use date_type instead
  bool is_everyday = 7 [deprecated = true];
  // The price of current add-on service
  double service_price = 8;
  // taxId
  int64 tax_id = 9;
  // duration
  int32 duration = 10;
  // createdAt
  google.protobuf.Timestamp created_at = 11;
  // updatedAt
  google.protobuf.Timestamp updated_at = 12;
  // quantity per day
  int32 quantity_per_day = 13;
  // date type
  models.appointment.v1.PetDetailDateType date_type = 14;
  // start date
  google.type.Date start_date = 15;
}
