syntax = "proto3";

package moego.api.online_booking.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// Parameters for updating an address.
// Fields are optional to allow for partial updates.
message UpsertAddressParams {
  // Online booking name or domain.
  oneof business_identifier {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // Unique identifier for the address, must be greater than 0 if provided.
  // If not provided, a new address will be created.
  optional int64 id = 3 [(validate.rules).int64.gt = 0];
  // Primary address line, required if provided.
  optional string address1 = 4 [(validate.rules).string = {max_len: 255}];
  // Secondary address line, optional and can be empty.
  optional string address2 = 5 [(validate.rules).string = {max_len: 255}];
  // City name, required if provided.
  optional string city = 6 [(validate.rules).string = {max_len: 255}];
  // Country name, required if provided.
  optional string country = 7 [(validate.rules).string = {max_len: 255}];
  // Latitude, must be within valid global coordinates if provided.
  optional string lat = 8 [(validate.rules).string = {max_len: 100}];
  // Longitude, must be within valid global coordinates if provided.
  optional string lng = 9 [(validate.rules).string = {max_len: 100}];
  // State or province.
  optional string state = 10 [(validate.rules).string = {max_len: 255}];
  // Postal or ZIP code, required if provided.
  optional string zipcode = 11 [(validate.rules).string = {max_len: 50}];
  // Whether this address is the primary address for the customer.
  optional int32 is_primary = 12 [(validate.rules).int32 = {
    in: [
      0,
      1
    ]
  }];
  // Whether this address is the profile request address.
  optional bool is_profile_request_address = 13;
}

// Result of an address upsert.
message UpsertAddressResult {
  // Id of the address.
  int64 id = 1;
  // Unique identifier for the customer associated with this address.
  int64 customer_id = 2;
  // Primary address line.
  string address1 = 3;
  // Secondary address line.
  string address2 = 4;
  // City name.
  string city = 5;
  // Country name.
  string country = 6;
  // Latitude.
  string lat = 7;
  // Longitude.
  string lng = 8;
  // State or province.
  string state = 9;
  // Postal or ZIP code.
  string zipcode = 10;
  // Whether this address is the primary address for the customer.
  int32 is_primary = 11;
  // Whether this address is the profile request address,
  // if true, id uses profile_request_address id,
  // if false, id uses business_customer_address id.
  bool is_profile_request_address = 12;
}

// Online Booking Address Service.
service OBAddressService {
  // Upsert an address.
  rpc UpsertAddress(UpsertAddressParams) returns (UpsertAddressResult) {
    // OB 引入 boarding/daycare 之后，这个接口迁移到了 OBClientService.UpdateOBClient 接口
    option deprecated = true;
  }
}
