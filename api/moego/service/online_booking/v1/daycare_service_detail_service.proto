syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v1/struct.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create DaycareServiceDetail request
message CreateDaycareServiceDetailRequest {
  // The id of pet, associated with the current service
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // The id of current service
  int64 service_id = 3 [(validate.rules).int64 = {gt: 0}];
  // The specific dates of the daycare service
  // 当 daycare service detail 加入 waitlist 时，这个参数可以为空
  repeated string specific_dates = 4 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];
  // The price of current service
  optional double service_price = 5;
  // taxId
  optional int64 tax_id = 6;
  // The max duration of the daycare service, unit minute
  optional int32 max_duration = 7;
  // The pet arrival date of the service, yyyy-MM-dd
  //  optional string start_date = 8 [(validate.rules).string = {max_len: 2048}];
  // The pet arrival time of the service, unit minute, 540 means 09:00
  // 当 daycare service detail 加入 waitlist 时，这个参数可以为空
  optional int32 start_time = 9 [(validate.rules).int32 = {gte: 0}];
  // The pet latest pickup date of the service, yyyy-MM-dd
  //  optional string end_date = 10 [(validate.rules).string = {max_len: 2048}];
  // The pet pickup time of the service, unit minute, 540 means 09:00
  // 当 daycare service detail 加入 waitlist 时，这个参数可以为空
  optional int32 end_time = 11 [(validate.rules).int32 = {gte: 0}];
  // createdAt
  optional google.protobuf.Timestamp created_at = 12;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 13;
}

// DaycareServiceDetail service
service DaycareServiceDetailService {}

// Update DaycareServiceDetail request
message UpdateDaycareServiceDetailRequest {
  // The id of daycare service detail
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // The specific dates of the daycare service
  optional moego.utils.v1.StringListValue specific_dates = 2;
  // The pet arrival time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 3 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // The pet latest pickup time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
}
