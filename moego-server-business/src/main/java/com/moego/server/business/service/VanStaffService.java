package com.moego.server.business.service;

import static com.moego.common.utils.PageUtil.hasEmptyCollectionFilter;
import static com.moego.common.utils.PageUtil.selectPage;
import static com.moego.server.business.common.consts.DataSourceConst.READER;

import com.moego.common.dto.SortDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.SortUtils;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.VanDTO;
import com.moego.server.business.dto.VanSmartScheduleRelationDTO;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapper.MoeVanMapper;
import com.moego.server.business.mapper.MoeVanStaffMapper;
import com.moego.server.business.mapperbean.MoeVan;
import com.moego.server.business.mapperbean.MoeVanExample;
import com.moego.server.business.mapperbean.MoeVanStaff;
import com.moego.server.business.params.DescribeVansParams;
import com.moego.server.business.params.SaveVanSmartScheduleRelationParams;
import com.moego.server.business.service.dto.van.VanHistoryDto;
import com.moego.server.business.service.dto.van.VanListDto;
import com.moego.server.business.web.vo.van.VanInsertVo;
import com.moego.server.business.web.vo.van.VanTransferStaffVo;
import com.moego.server.business.web.vo.van.VanUpdateVo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class VanStaffService {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private MoeVanMapper vanMapper;

    @Autowired
    private StaffService staffService;

    @Autowired
    private MoeBusinessMapper moeBusinessMapper;

    @Autowired
    private MoeVanStaffMapper vanStaffMapper;

    @Autowired
    private SmartScheduleSettingService smartScheduleSettingService;

    @Autowired
    private BusinessService businessService;

    public void checkVanIsBelongBusiness(Integer businessId, Integer vanId) {
        MoeVan vanBean = vanMapper.selectByPrimaryKey(vanId);
        if (vanBean == null || !Objects.equals(businessId, vanBean.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "vanId invalid");
        }
    }

    public List<VanDTO> queryVanByIdList(Integer businessId, List<Integer> vanIdList) {
        if (CollectionUtils.isEmpty(vanIdList)) {
            return List.of();
        }
        MoeVanExample example = new MoeVanExample();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andIdIn(vanIdList)
                .andIsDeletedEqualTo(BooleanEnum.VALUE_FALSE);
        example.setOrderByClause("sort DESC");

        return vanMapper.selectByExample(example).stream()
                .map(van -> {
                    VanDTO vanDTO = new VanDTO();
                    BeanUtils.copyProperties(van, vanDTO);
                    vanDTO.setVanId(van.getId());
                    return vanDTO;
                })
                .collect(Collectors.toList());
    }

    public Map<Integer, List<Integer>> queryVanStaffRelation(Integer businessId, List<Integer> vanIdList) {
        if (CollectionUtils.isEmpty(vanIdList)) {
            return Collections.emptyMap();
        }
        return vanStaffMapper.selectByVanIdList(businessId, vanIdList).stream()
                .collect(Collectors.groupingBy(
                        MoeVanStaff::getVanId, Collectors.mapping(MoeVanStaff::getStaffId, Collectors.toList())));
    }

    /**
     * 查询 van staff 关系
     *
     * @param businessId businessId
     * @param staffIdList staffIdList
     * @return key - staffId, value - vanId
     */
    public Map<Integer, Integer> queryStaffRelation(Integer businessId, List<Integer> staffIdList) {
        return vanStaffMapper.useDataSource(READER).selectByStaffIdList(businessId, staffIdList).stream()
                .collect(Collectors.toMap(MoeVanStaff::getStaffId, MoeVanStaff::getVanId, (r1, r2) -> r1));
    }

    public List<VanListDto> queryVanListDto(Long companyId, Long tokenBusinessId) {
        Integer businessId;
        try {
            businessId = businessService.getBusinessIdForMobile(companyId, tokenBusinessId);
        } catch (Exception e) {
            log.warn("get mobile business id error", e);
            return Collections.emptyList();
        }
        List<MoeVan> vanList = vanMapper.selectByBusinessId(businessId);
        if (CollectionUtils.isEmpty(vanList)) {
            return Collections.emptyList();
        }
        List<VanListDto> vanListDtos = new ArrayList<>(vanList.size());
        List<Integer> vanIdList = vanList.stream().map(MoeVan::getId).collect(Collectors.toList());
        Map<Integer, List<Integer>> vanStaffIdListMap = queryVanStaffRelation(businessId, vanIdList);
        List<Integer> staffIds = vanStaffIdListMap.values().stream()
                .flatMap(Collection::stream)
                .distinct()
                .toList();
        Map<Integer, MoeStaffDto> staffMap = staffService.getStaffMapByIds(companyId, staffIds);

        Map<Integer, VanSmartScheduleRelationDTO> vanSmartScheduleRelationMap =
                smartScheduleSettingService.getVanSmartScheduleRelationMap(businessId, vanIdList);

        vanList.forEach(van -> {
            VanListDto listDto = new VanListDto();
            listDto.setVanId(van.getId());
            BeanUtils.copyProperties(van, listDto);
            listDto.setStaffIdList(vanStaffIdListMap.getOrDefault(van.getId(), Collections.emptyList()).stream()
                    .distinct()
                    .toList());
            // 将 staff 信息返回，包括删除的 staff
            listDto.setStaffList(vanStaffIdListMap.getOrDefault(van.getId(), Collections.emptyList()).stream()
                    .map(staffMap::get)
                    .filter(Objects::nonNull)
                    .toList());
            if (vanSmartScheduleRelationMap.containsKey(van.getId())) {
                listDto.setDrivingRuleId(
                        vanSmartScheduleRelationMap.get(van.getId()).getDrivingRuleId());
                listDto.setLocationId(
                        vanSmartScheduleRelationMap.get(van.getId()).getLocationId());
            }
            vanListDtos.add(listDto);
        });
        return vanListDtos;
    }

    /**
     * 校验van数量，必须满足business设置的最大值
     *
     * @param companyId
     * @return
     */
    public Boolean checkVanNumIsEnough(Long companyId) {
        return companyService.queryCompanyRemainVanNumByCompanyId(companyId.intValue()) > 0;
    }

    @Transactional
    public Integer addVan(Long companyId, Long tokenBusinessId, VanInsertVo vo, Integer operatorId) {
        Integer businessId = businessService.getBusinessIdForMobile(companyId, tokenBusinessId);
        // check van name
        checkVanNickNameExist(businessId, vo.getNickName(), null);

        MoeVan newVan = new MoeVan();
        BeanUtils.copyProperties(vo, newVan);
        newVan.setCreateTime(DateUtil.get10Timestamp());
        newVan.setUpdateTime(DateUtil.get10Timestamp());
        newVan.setBusinessId(businessId);
        newVan.setCompanyId(companyId);
        vanMapper.insertSelective(newVan);
        // 更新van绑定的staffId
        if (vo.getStaffIdList() != null) {
            updateVanStaffRelation(companyId, businessId, newVan.getId(), vo.getStaffIdList());
        }
        // 更新 van 绑定的 smartScheduleSetting
        if (vo.getDrivingRuleId() != null || vo.getLocationId() != null) {
            smartScheduleSettingService.saveVanSmartScheduleSettingRelation(
                    companyId,
                    new SaveVanSmartScheduleRelationParams()
                            .setVanId(newVan.getId())
                            .setBusinessId(businessId)
                            .setDrivingRuleId(vo.getDrivingRuleId())
                            .setLocationId(vo.getLocationId())
                            .setOperatorId(operatorId));
        }
        return newVan.getId();
    }

    @Transactional
    public void updateVan(Long companyId, Long tokenBusinessId, VanUpdateVo vo, Integer operatorId) {
        Integer businessId = businessService.getBusinessIdForMobile(companyId, tokenBusinessId);
        // 检查van所属关系
        checkVanIsBelongBusiness(businessId, vo.getVanId());
        // check van name
        checkVanNickNameExist(businessId, vo.getNickName(), vo.getVanId());

        // 更新van属性
        MoeVan newVanBean = new MoeVan();
        BeanUtils.copyProperties(vo, newVanBean);
        newVanBean.setId(vo.getVanId());
        newVanBean.setUpdateTime(DateUtil.get10Timestamp());
        vanMapper.updateByPrimaryKeySelective(newVanBean);
        // 更新van绑定的staffId
        if (vo.getStaffIdList() != null) {
            updateVanStaffRelation(companyId, businessId, vo.getVanId(), vo.getStaffIdList());
        }
        // 更新 van 绑定的 smartScheduleSetting
        if (vo.getDrivingRuleId() != null || vo.getLocationId() != null) {
            smartScheduleSettingService.saveVanSmartScheduleSettingRelation(
                    companyId,
                    new SaveVanSmartScheduleRelationParams()
                            .setVanId(vo.getVanId())
                            .setBusinessId(businessId)
                            .setDrivingRuleId(vo.getDrivingRuleId())
                            .setLocationId(vo.getLocationId())
                            .setOperatorId(operatorId));
        }
    }

    @Transactional
    public void deleteVan(Long companyId, Long tokenBusinessId, Integer vanId) {
        Integer businessId = businessService.getBusinessIdForMobile(companyId, tokenBusinessId);
        // 检查van所属关系
        checkVanIsBelongBusiness(businessId, vanId);
        // 将van标记为删除
        MoeVan newVanBean = new MoeVan();
        newVanBean.setUpdateTime(DateUtil.get10Timestamp());
        newVanBean.setIsDeleted(DeleteStatusEnum.IS_DELETE_TRUE);
        newVanBean.setId(vanId);
        vanMapper.updateByPrimaryKeySelective(newVanBean);

        // 解除van绑定的staffId
        updateVanStaffRelation(companyId, businessId, vanId, Collections.emptyList());
    }

    /**
     * 通过staffId 删除所有的绑定记录
     * 目前用于删除staff的时候，处理van的数据
     *
     * @param businessId
     * @param staffId
     */
    @Transactional
    public void deleteStaff(Integer businessId, Integer staffId) {
        vanStaffMapper.setVanStaffRecordEndByStaffId(businessId, staffId, DateUtil.get10Timestamp());
    }

    public void sortVan(Long companyId, Long tokenBusinessId, List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<SortDto> sortDtos = SortUtils.sort(idList);
        vanMapper.sortVan(businessService.getBusinessIdForMobile(companyId, tokenBusinessId), sortDtos);
    }

    @Transactional
    public void transferStaff(Long companyId, Long tokenBusinessId, VanTransferStaffVo staffVo) {
        Integer businessId = businessService.getBusinessIdForMobile(companyId, tokenBusinessId);
        // insert的时候，需要校验staffId只绑定一个van
        checkStaffBandingVan(businessId, staffVo.getStaffId(), staffVo.getFromVanId());
        List<MoeVanStaff> fromVanBandingRecord =
                vanStaffMapper.selectByVanIdStaffId(businessId, staffVo.getFromVanId(), staffVo.getStaffId());
        if (CollectionUtils.isEmpty(fromVanBandingRecord)) {
            // 未在from van找到staff记录
            throw new CommonException(ResponseCodeEnum.STAFF_NOT_FOUND_IN_FROM_VAN);
        }
        List<MoeVanStaff> toVanBandingRecord =
                vanStaffMapper.selectByVanIdStaffId(businessId, staffVo.getToVanId(), staffVo.getStaffId());
        if (!CollectionUtils.isEmpty(toVanBandingRecord)) {
            // 该staff记录已经在to van
            throw new CommonException(ResponseCodeEnum.STAFF_FOUND_IN_TO_VAN);
        }
        Long nowTime = DateUtil.get10Timestamp();
        List<Integer> staffIdList = Collections.singletonList(staffVo.getStaffId());
        // from van remove this staff
        vanStaffMapper.setVanStaffRecordEndWithStaffList(businessId, staffVo.getFromVanId(), nowTime, staffIdList);
        // to van add this staff
        MoeVanStaff vanStaffInsertBean = new MoeVanStaff();
        vanStaffInsertBean.setCompanyId(companyId);
        vanStaffInsertBean.setBusinessId(businessId);
        vanStaffInsertBean.setVanId(staffVo.getToVanId());
        vanStaffInsertBean.setStaffId(staffVo.getStaffId());
        vanStaffInsertBean.setStartTime(nowTime);
        vanStaffInsertBean.setCreateTime(nowTime);
        vanStaffInsertBean.setUpdateTime(nowTime);
        vanStaffMapper.insertSelective(vanStaffInsertBean);
    }

    public List<VanHistoryDto> queryVanStaffHistory(Long companyId, Long tokenBusinessId, Integer vanId) {
        Integer businessId = businessService.getBusinessIdForMobile(companyId, tokenBusinessId);
        checkVanIsBelongBusiness(businessId, vanId);
        List<MoeVanStaff> vanStaffList = vanStaffMapper.selectHistoryByVanId(businessId, vanId);
        if (!CollectionUtils.isEmpty(vanStaffList)) {
            List<VanHistoryDto> historyDtos = new ArrayList<>();
            vanStaffList.forEach(vanStaff -> historyDtos.add(new VanHistoryDto(
                    vanStaff.getStaffId(), vanStaff.getStartTime(), vanStaff.getEndTime(), vanStaff.getCreateTime())));
            return historyDtos;
        }
        return Collections.emptyList();
    }

    /**
     * 更新van和van staff 关系绑定
     * 安全：调用前需校验 van 所属
     *
     * @param businessId
     * @param vanId
     * @param staffIdList
     */
    @Transactional
    public void updateVanStaffRelation(Long companyId, Integer businessId, Integer vanId, List<Integer> staffIdList) {
        Long nowTime = DateUtil.get10Timestamp();
        if (CollectionUtils.isEmpty(staffIdList)) {
            // 删除所有关系
            vanStaffMapper.setVanStaffRecordEndByVanId(businessId, vanId, nowTime);
        } else {
            // check staffId
            Map<Integer, MoeStaffDto> staffMap = staffService.getStaffMapByIds(companyId, staffIdList);
            if (CollectionUtils.isEmpty(staffMap)) {
                return;
            }
            List<MoeVanStaff> existingVanStaffList = vanStaffMapper.selectByVanId(businessId, vanId);
            // 将查询的数据，只保留有效的staffId
            List<Integer> needCheckStaffIdList = new ArrayList<>(staffMap.keySet());
            List<Integer> exstingStaffIdList =
                    existingVanStaffList.stream().map(MoeVanStaff::getStaffId).collect(Collectors.toList());
            // 计算&更新关系
            // batch insert
            List<MoeVanStaff> vanStaffList = new ArrayList<>();
            needCheckStaffIdList.stream()
                    .filter(staffId -> !exstingStaffIdList.contains(staffId))
                    .forEach(staffId -> {
                        MoeVanStaff vanStaffInsertBean = new MoeVanStaff();
                        vanStaffInsertBean.setCompanyId(companyId);
                        vanStaffInsertBean.setBusinessId(businessId);
                        vanStaffInsertBean.setVanId(vanId);
                        vanStaffInsertBean.setStaffId(staffId);
                        vanStaffInsertBean.setStartTime(nowTime);
                        vanStaffInsertBean.setCreateTime(nowTime);
                        vanStaffInsertBean.setUpdateTime(nowTime);
                        vanStaffList.add(vanStaffInsertBean);
                    });
            // batch delete
            List<Integer> needDeleteStaffIdList = exstingStaffIdList.stream()
                    .filter(staffId -> !needCheckStaffIdList.contains(staffId))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(vanStaffList)) {
                // insert的时候，需要校验staffId只绑定一个van
                checkStaffBandingVan(businessId, staffIdList, vanId);
                vanStaffMapper.batchInsert(vanStaffList);
            }
            if (!CollectionUtils.isEmpty(needDeleteStaffIdList)) {
                vanStaffMapper.setVanStaffRecordEndWithStaffList(businessId, vanId, nowTime, needDeleteStaffIdList);
            }
        }
    }

    /**
     * 确保staff只能绑定一个van
     *
     * @param businessId
     * @param excludeVanId
     */
    private void checkStaffBandingVan(Integer businessId, Integer staffId, Integer excludeVanId) {
        int queryStaffCount = vanStaffMapper.queryStaffBindingVanCount(businessId, staffId, excludeVanId);
        if (queryStaffCount > 0) {
            throw new CommonException(ResponseCodeEnum.STAFF_ONLY_BANDING_ONE_VAN);
        }
    }

    /**
     * 确保staff list只能绑定一个van
     *
     * @param businessId
     * @param excludeVanId
     */
    private void checkStaffBandingVan(Integer businessId, List<Integer> staffIdList, Integer excludeVanId) {
        int queryStaffCount = vanStaffMapper.queryStaffListBindingVanCount(businessId, staffIdList, excludeVanId);
        if (queryStaffCount > 0) {
            throw new CommonException(ResponseCodeEnum.STAFF_ONLY_BANDING_ONE_VAN);
        }
    }

    private void checkVanNickNameExist(Integer businessId, String nickName, Integer exceptId) {
        //  为空时不检查
        if (nickName == null) {
            return;
        }
        MoeVanExample example = new MoeVanExample();
        MoeVanExample.Criteria criteria = example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andNickNameEqualTo(nickName)
                .andIsDeletedEqualTo(BooleanEnum.VALUE_FALSE);
        if (exceptId != null) {
            criteria.andIdNotEqualTo(exceptId);
        }
        if (vanMapper.countByExample(example) > 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Van nickname exist");
        }
    }

    private void checkVanLicensePlateExist(Integer businessId, String licensePlate, Integer exceptId) {
        if (licensePlate == null) {
            return;
        }
        if (!StringUtils.hasText(licensePlate)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Van license plate could not be empty");
        }
        MoeVanExample example = new MoeVanExample();
        MoeVanExample.Criteria criteria = example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andLicensePlateEqualTo(licensePlate)
                .andIsDeletedEqualTo(BooleanEnum.VALUE_FALSE);
        if (exceptId != null) {
            criteria.andIdNotEqualTo(exceptId);
        }
        if (vanMapper.countByExample(example) > 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Van license plate exist");
        }
    }

    public Map<Integer, Integer> getVanStaffBindingsByIds(List<Integer> businessIds, List<Integer> staffIds) {
        return vanStaffMapper.getVanStaffBindingsByIds(businessIds, staffIds).stream()
                .collect(Collectors.toMap(MoeVanStaff::getStaffId, MoeVanStaff::getVanId));
    }

    public Pair<List<MoeVan>, Pagination> describeVans(DescribeVansParams params) {
        if (hasEmptyCollectionFilter(params.ids(), params.businessIds())) {
            return Pair.of(
                    Collections.emptyList(),
                    new Pagination(
                            params.pagination().pageNum(), params.pagination().pageSize(), 0));
        }
        return selectPage(params.pagination(), () -> vanMapper.describeVans(params));
    }
}
