package com.moego.server.business.server;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerGroup;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetrics;
import com.moego.server.business.api.IBusinessReferralServiceBase;
import com.moego.server.business.dto.ReferralInfoDTO;
import com.moego.server.business.dto.ReferralStatusDTO;
import com.moego.server.business.params.ReferralInfoParams;
import com.moego.server.business.service.ReferralService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/6/30
 */
@RestController
@AllArgsConstructor
public class BusinessReferralServer extends IBusinessReferralServiceBase {

    private final ReferralService referralService;

    private final LockManager lockManager;

    @Override
    public void updateReferralInfo(@RequestBody ReferralInfoParams referralParams) {
        referralService.updateReferralInfo(referralParams);
    }

    @Override
    public void cancelReferral(@RequestParam("companyId") Integer companyId) {
        referralService.cancelReferral(companyId);
    }

    @Override
    public void activeReferral(@RequestParam("companyId") Integer companyId) {
        referralService.activeReferral(companyId);
    }

    @Override
    @TimerMetrics(group = TimerGroup.TASK)
    public void syncUpgradedToSuccess() {
        String resourceKey = lockManager.getResourceKey(LockManager.BUSINESS_REFERRAL, 0);
        String value = CommonUtil.getUuid();
        try {
            if (lockManager.lock(resourceKey, value)) {
                referralService.syncUpgradedToSuccess();
            } else {
                throw new CommonException(ResponseCodeEnum.PARALLEL_ERROR, "get lock failed for " + resourceKey);
            }
        } finally {
            lockManager.unlock(resourceKey, value);
        }
    }

    @Override
    @TimerMetrics(group = TimerGroup.TASK)
    public void syncSuccessToOver3Month() {
        String resourceKey = lockManager.getResourceKey(LockManager.BUSINESS_REFERRAL, 1);
        String value = CommonUtil.getUuid();
        try {
            if (lockManager.lock(resourceKey, value)) {
                referralService.syncSuccessToOver3Month();
            } else {
                throw new CommonException(ResponseCodeEnum.PARALLEL_ERROR, "get lock failed for " + resourceKey);
            }
        } finally {
            lockManager.unlock(resourceKey, value);
        }
    }

    @Override
    public void syncStripeBalance() {
        referralService.syncStripeBalance();
    }

    @Override
    public ReferralStatusDTO getReferralStatus(
            @RequestParam("accountId") Integer accountId,
            @RequestParam("companyId") Integer companyId,
            @RequestParam("referralCode") String referralCode) {
        return referralService.getReferralStatus(ReferralInfoParams.builder()
                .accountId(accountId)
                .companyId(companyId)
                .referralCode(referralCode)
                .build());
    }

    @Override
    public void syncReferralEndStatus(@RequestParam("companyId") Integer companyId) {
        referralService.syncReferralEndStatus(companyId);
    }

    @Override
    public ReferralInfoDTO getReferralInfo(
            @RequestParam("companyId") Integer companyId, @RequestParam("accountId") Integer accountId) {
        return referralService.getRefererInfo(companyId, accountId);
    }

    @Override
    public void resetReferralBalance(
            @RequestParam("companyId") Integer companyId, @RequestParam("accountId") Integer accountId) {
        referralService.resetReferralBalance(companyId, accountId);
    }
}
