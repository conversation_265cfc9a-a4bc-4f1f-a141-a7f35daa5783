package com.moego.server.business.service;

import static java.util.Comparator.comparing;

import com.moego.common.enums.ReportConst;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.consts.report.ReportCategory;
import com.moego.server.business.consts.report.ReportType;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapper.MoeBusinessReportMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeBusinessReport;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.service.util.ReportBeanUtil;
import com.moego.server.business.web.param.QueryReportParams;
import com.moego.server.business.web.vo.report.AppointmentNumbersVo;
import com.moego.server.business.web.vo.report.AppointmentReportRecord;
import com.moego.server.business.web.vo.report.DashboardOverviewVo;
import com.moego.server.business.web.vo.report.DashboardSummaryData;
import com.moego.server.business.web.vo.report.DashboardSummaryVo;
import com.moego.server.business.web.vo.report.EmployeeReportRecord;
import com.moego.server.business.web.vo.report.GetReportResponse;
import com.moego.server.business.web.vo.report.PetReportRecord;
import com.moego.server.business.web.vo.report.ReportRecordByBusinessId;
import com.moego.server.business.web.vo.report.SaleReportRecord;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.client.IGroomingReportClient;
import com.moego.server.grooming.dto.report.EmployeeContribute;
import com.moego.server.grooming.dto.report.GroomingMobileOverviewDTO;
import com.moego.server.grooming.dto.report.ReportApptsNumberDTO;
import com.moego.server.grooming.dto.report.ReportCollectedRevenueDTO;
import com.moego.server.grooming.dto.report.ReportExpectedRevenueDTO;
import com.moego.server.grooming.dto.report.ReportPaymentSummaryDto;
import com.moego.server.grooming.dto.report.ReportRecurringCustomerDTO;
import com.moego.server.grooming.dto.report.ReportTrendDTO;
import com.moego.server.grooming.dto.report.ReportTrendDataDTO;
import com.moego.server.grooming.dto.report.ReportWebSaleService;
import com.moego.server.grooming.params.DateRangeParams;
import com.moego.server.grooming.params.report.GetDashboardOverviewParams;
import com.moego.server.grooming.params.report.GetReportTrendParams;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * ReportV2Service
 * 支持查询多个 business 的数据，暂时和 ReportService 并存
 */
@Slf4j
@Service
public class ReportV2Service {

    @Autowired
    private MoeBusinessMapper businessMapper;

    @Autowired
    private MoeBusinessReportMapper reportMapper;

    @Autowired
    private ReportService reportService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private IGroomingReportClient groomingReportClient;

    @Autowired
    private IGroomingAppointmentClient groomingAppointmentClient;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private PermissionHelper permissionHelper;

    public List<Long> checkAndGetBusinessIds(QueryReportParams params) {
        Boolean isAllLocation = params.getIsAllLocation();
        List<Long> businessIds = params.getBusinessIds();
        Set<Long> maxLocationIds = permissionHelper.getMaxLocationPermission(
                params.getTokenCompanyId(), params.getTokenStaffId(), PermissionEnums.ACCESS_REPORT);
        if (Boolean.TRUE.equals(isAllLocation)) {
            return new ArrayList<>(maxLocationIds);
        }
        if (CollectionUtils.isEmpty(businessIds)) {
            // businessIds 为空，且 isAllLocation 为 false，报错
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "IsAllLocation and businessIds is empty");
        }
        businessIds.retainAll(maxLocationIds);
        if (CollectionUtils.isEmpty(businessIds)) {
            // 传了异常 businessId
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Business id error");
        }
        return new ArrayList<>(businessIds);
    }

    public DashboardSummaryVo getDashboardSummary(QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        boolean isMigrate = migrateHelper.isMigrate(tokenCompanyId);
        List<Long> businessIds = isMigrate ? checkAndGetBusinessIds(params) : List.of(businessId.longValue());

        if (businessIds.size() == 1) {
            // 传一个 businessId 时，走老的逻辑
            return reportService.getDashboardSummary(businessIds.get(0).intValue(), startDate, endDate);
        }

        List<DashboardSummaryData> reportDataList = businessIds.stream()
                .map(bId -> CompletableFuture.supplyAsync(
                        () -> reportService.getDashboardSummaryData(bId.intValue(), startDate, endDate)))
                .map(CompletableFuture::join)
                .toList();

        return ReportBeanUtil.mergeDashboardSummaryData(reportDataList);
    }

    public DashboardOverviewVo getDashboardOverview(QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        List<Long> businessIds =
                migrateInfo.isMigrate() ? checkAndGetBusinessIds(params) : List.of(businessId.longValue());

        if (businessIds.size() == 1) {
            // 传一个 businessId 时，走老的逻辑
            return reportService.getDashboardOverview(businessIds.get(0).intValue(), startDate, endDate, migrateInfo);
        }

        List<MoeStaff> staffs = staffService.getStaffListByCompanyId(tokenCompanyId, true);
        List<EmployeeContribute> employees = staffs.stream()
                .map(s -> EmployeeContribute.builder()
                        .id(s.getId())
                        .name((s.getFirstName() + " " + s.getLastName()).trim())
                        .build())
                .collect(Collectors.toList());
        GetDashboardOverviewParams queryParams = new GetDashboardOverviewParams()
                .setBusinessIds(businessIds)
                .setCompanyId(tokenCompanyId)
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setStaffs(employees);
        GroomingMobileOverviewDTO reportData = groomingReportClient.getMobileDashboardOverviewV2(queryParams);

        List<Integer> staffIds = reportData.getEmployeeOverview().stream()
                .map(EmployeeContribute::getId)
                .toList();
        Map<Integer, MoeStaffDto> staffMap = staffService.getStaffMapByIds(tokenCompanyId, staffIds);

        // 填充员工姓名
        List<EmployeeContribute> employeeOverview = reportData.getEmployeeOverview().stream()
                .filter(e -> staffMap.containsKey(e.getId()))
                .peek(e -> {
                    MoeStaffDto staff = staffMap.get(e.getId());
                    e.setName(ReportBeanUtil.buildName(staff.getFirstName(), staff.getLastName()));
                })
                .toList();

        return DashboardOverviewVo.builder()
                .recurringClientPercentage(reportData.getRecurringClientPercentage())
                .topSpendingClients(reportData.getTopSpendingClients())
                .employeeOverview(employeeOverview)
                .build();
    }

    public AppointmentNumbersVo getReportApptsNumber(QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        boolean isMigrate = migrateHelper.isMigrate(tokenCompanyId);
        List<Long> businessIds = isMigrate ? checkAndGetBusinessIds(params) : List.of(businessId.longValue());

        if (businessIds.size() == 1) {
            // 传一个 businessId 时，走老的逻辑
            return reportService.getReportApptsNumber(businessIds.get(0).intValue(), startDate, endDate);
        }
        List<ReportApptsNumberDTO> reportDataList = businessIds.stream()
                .map(bId -> CompletableFuture.supplyAsync(
                        () -> groomingReportClient.getReportApptsNumber(bId.intValue(), startDate, endDate)))
                .map(CompletableFuture::join)
                .toList();

        return ReportBeanUtil.mergeApptNumbersVo(reportDataList);
    }

    public GetReportResponse getReport(Integer reportId, QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        boolean isMigrate = migrateHelper.isMigrate(tokenCompanyId);
        List<Long> businessIds = isMigrate ? checkAndGetBusinessIds(params) : List.of(businessId.longValue());

        if (businessIds.size() == 1) {
            // 传一个 businessId 时，走老的逻辑
            return reportService.getReport(reportId, businessIds.get(0).intValue(), startDate, endDate, tokenCompanyId);
        }

        ReportCategory category = ReportType.getCategoryById(reportId);
        if (category == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid report id");
        }
        List<Map<String, Object>> records =
                switch (category) {
                    case APPOINTMENT -> queryApptReport(businessIds, reportId, startDate, endDate);
                    case EMPLOYEE -> queryEmployeeReport(tokenCompanyId, businessIds, reportId, startDate, endDate);
                    case SALE -> querySalesReport(businessIds, reportId, startDate, endDate);
                    case CLIENT -> queryClientReport(businessIds, reportId, startDate, endDate);
                    case PET -> queryPetReport(businessIds, reportId, startDate, endDate);
                    case PRODUCT -> queryProductReport(businessIds, reportId, startDate, endDate);
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid category");
                };

        // 迁移前：根据 businessId 查询，迁移后：根据 companyId 查询，合并结果，这里是迁移后的逻辑，迁移前的在前面走老的逻辑了
        List<MoeBusinessReport> report = reportMapper.selectByCompanyIdAndReportId(tokenCompanyId, reportId);
        boolean isFavorite = report.stream().anyMatch(r -> Boolean.TRUE.equals(r.getIsFavorite()));

        return reportService
                .reportMetadataBuilder(businessId, reportId, category, records)
                .isFavorite(isFavorite)
                .records(records)
                .build();
    }

    private List<Map<String, Object>> queryApptReport(
            List<Long> businessIds, Integer reportId, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return List.of();
        }

        List<ReportRecordByBusinessId> resultList = businessIds.stream()
                .map(businessId -> CompletableFuture.supplyAsync(() ->
                        reportService.queryApptReportByBusinessId(businessId.intValue(), reportId, startDate, endDate)))
                .map(CompletableFuture::join)
                .sorted(Comparator.comparing(ReportRecordByBusinessId::getBusinessId))
                .toList();

        return resultList.stream()
                .map(ReportRecordByBusinessId::getReportRecordList)
                .flatMap(Collection::stream)
                .toList();
    }

    private List<Map<String, Object>> queryEmployeeReport(
            Long companyId, List<Long> businessIds, Integer reportId, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return List.of();
        }
        List<EmployeeReportRecord> resultList = businessIds.stream()
                .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.queryEmployeeReportRecord(
                        companyId, businessId.intValue(), reportId, startDate, endDate)))
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .toList();
        // employee full report, clock in/out, tips by employee 需要合并数据、根据 staff id 排序
        // employee payroll report 以预约维度，根据 staff name、appt date、time 排序
        ReportType reportType = ReportType.getById(reportId);
        if (reportType == ReportType.EMPLOYEE_PAYROLL_REPORT) {
            return resultList.stream()
                    .sorted(
                            // 排序：employeeName > apptDate > apptTime
                            comparing(EmployeeReportRecord::getEmployeeName)
                                    .thenComparing(comparing(EmployeeReportRecord::getApptDate)
                                            .reversed()) // 日期倒序
                                    .thenComparing(comparing(EmployeeReportRecord::getApptTime)
                                            .reversed()) // 预约时间倒序
                            )
                    .map(CommonUtil::transBean2Map)
                    .toList();
        } else {
            return ReportBeanUtil.mergeEmployeeReportData(resultList, reportType).stream()
                    .map(CommonUtil::transBean2Map)
                    .toList();
        }
    }

    private List<Map<String, Object>> querySalesReport(
            List<Long> businessIds, Integer reportId, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return List.of();
        }

        ReportType reportType = ReportType.getById(reportId);
        if (reportType == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid report id");
        }
        List<Map<String, Object>> recordList;
        switch (reportType) {
            case SALES_BY_EMPLOYEE -> // sales by employee 不需要合并数据，只需要重新排序
            recordList = businessIds.stream()
                    .map(businessId -> CompletableFuture.supplyAsync(() ->
                            reportService.getSalesByEmployeeData(businessId.intValue(), reportId, startDate, endDate)))
                    .map(CompletableFuture::join)
                    .flatMap(Collection::stream)
                    .sorted((a, b) -> {
                        // 排序：employeeName > apptDate > apptTime
                        if (!a.getEmployeeName().equals(b.getEmployeeName())) {
                            return a.getEmployeeName()
                                    .toLowerCase()
                                    .compareTo(b.getEmployeeName().toLowerCase());
                        }
                        if (!b.getApptDate().equals(a.getApptDate())) {
                            return b.getApptDate().compareTo(a.getApptDate());
                        }
                        return b.getApptTime().compareTo(a.getApptTime());
                    })
                    .map(CommonUtil::transBean2Map)
                    .toList();
            case SALES_BY_WEEK -> {
                List<SaleReportRecord> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.getSalesByPeriodData(
                                businessId.intValue(), reportId, startDate, endDate, ReportConst.SALES_REPORT_BY_WEEK)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByWeek(reportDataList).stream()
                        .sorted(reportService.getPeriodComparator())
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
            case SALES_BY_MONTH -> {
                List<SaleReportRecord> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.getSalesByPeriodData(
                                businessId.intValue(),
                                reportId,
                                startDate,
                                endDate,
                                ReportConst.SALES_REPORT_BY_MONTH)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByMonth(reportDataList).stream()
                        .sorted(reportService.getPeriodComparator())
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
            case SALES_BY_CLIENT -> {
                List<SaleReportRecord> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.getSalesByClientData(
                                businessId.intValue(), reportId, startDate, endDate)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByClient(reportDataList).stream()
                        .sorted(comparing(a -> (a.getClientFirstName() + a.getClientLastName()).toLowerCase()))
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
            case SALES_BY_DAY_OF_WEEK -> {
                List<SaleReportRecord> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.getSalesByPeriodData(
                                businessId.intValue(),
                                reportId,
                                startDate,
                                endDate,
                                ReportConst.SALES_REPORT_BY_DAY_OF_WEEK)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByWeekDay(reportDataList).stream()
                        .sorted(reportService.getPeriodComparator())
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
            case SALES_BY_CITY_AND_DEMOGRAPHICS -> {
                List<SaleReportRecord> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.getSalesByGeoData(
                                businessId.intValue(), reportId, startDate, endDate, ReportConst.SALES_REPORT_BY_CITY)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByCity(reportDataList).stream()
                        .sorted(reportService.getGeoSaleReportSortComparator(ReportConst.SALES_REPORT_BY_CITY))
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
            case SALES_BY_ZIPCODE_AND_DEMOGRAPHICS -> {
                List<SaleReportRecord> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.getSalesByGeoData(
                                businessId.intValue(),
                                reportId,
                                startDate,
                                endDate,
                                ReportConst.SALES_REPORT_BY_ZIPCODE)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByZipcode(reportDataList).stream()
                        .sorted(reportService.getGeoSaleReportSortComparator(ReportConst.SALES_REPORT_BY_ZIPCODE))
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
                // Sales and appointments report，appt 不需要合并
            case SALES_AND_APPOINTMENTS_REPORT -> recordList = businessIds.stream()
                    .map(businessId -> CompletableFuture.supplyAsync(() ->
                            reportService.queryApptReportData(businessId.intValue(), reportId, startDate, endDate)))
                    .map(CompletableFuture::join)
                    .flatMap(Collection::stream)
                    .sorted(Comparator.comparing(AppointmentReportRecord::getApptDate))
                    .map(CommonUtil::transBean2Map)
                    .toList();
                // Sales by services
            case SALES_BY_SERVICES -> {
                List<ReportWebSaleService> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(
                                () -> reportService.querySalesServiceData(businessId.intValue(), startDate, endDate)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByService(reportDataList).stream()
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
                // Sales by payment methods
            case SALES_BY_PAYMENT_METHODS -> {
                List<ReportPaymentSummaryDto> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(() ->
                                reportService.querySalesPaymentMethodData(businessId.intValue(), startDate, endDate)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByPaymentMethod(reportDataList).stream()
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
                // Sales by service area
            case SALES_BY_SERVICE_AREA_AND_DEMOGRAPHICS -> {
                List<SaleReportRecord> reportDataList = businessIds.stream()
                        .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.getSalesByGeoData(
                                businessId.intValue(),
                                reportId,
                                startDate,
                                endDate,
                                ReportConst.SALES_REPORT_BY_SERVICEAREA)))
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .toList();
                recordList = ReportBeanUtil.mergeSaleReportDataByServiceArea(reportDataList).stream()
                        .sorted(reportService.getGeoSaleReportSortComparator(ReportConst.SALES_REPORT_BY_SERVICEAREA))
                        .map(CommonUtil::transBean2Map)
                        .toList();
            }
                // Sales by net income，不需要合并
            case SALES_BY_NET_INCOME -> recordList = businessIds.stream()
                    .map(businessId -> CompletableFuture.supplyAsync(
                            () -> reportService.queryNetIncomeReportData(businessId.intValue(), startDate, endDate)))
                    .map(CompletableFuture::join)
                    .flatMap(Collection::stream)
                    .sorted(Comparator.comparing(AppointmentReportRecord::getApptDate))
                    .map(CommonUtil::transBean2Map)
                    .toList();
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unexpected report Id: " + reportId);
        }
        return recordList;
    }

    private List<Map<String, Object>> queryClientReport(
            List<Long> businessIds, Integer reportId, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return List.of();
        }
        List<ReportRecurringCustomerDTO> resultList = businessIds.stream()
                .map(businessId -> CompletableFuture.supplyAsync(
                        () -> reportService.queryClientReport(businessId.intValue(), startDate, endDate)))
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .toList();

        return ReportBeanUtil.mergeClientReportData(resultList).stream()
                .map(CommonUtil::transBean2Map)
                .toList();
    }

    private List<Map<String, Object>> queryPetReport(
            List<Long> businessIds, Integer reportId, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return List.of();
        }
        // 查询整个 company 级别的数据
        List<PetReportRecord> resultList = businessIds.stream()
                .map(businessId -> CompletableFuture.supplyAsync(
                        () -> reportService.queryPetReportData(businessId.intValue(), reportId, startDate, endDate)))
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .toList();

        switch (Objects.requireNonNull(ReportType.getById(reportId))) {
            case NUMBER_OF_PETS_BY_BREED -> {
                resultList = ReportBeanUtil.mergePetReportDataByBreed(resultList);
                resultList.sort(Comparator.comparing(a -> (a.getType() + a.getBreed()).toLowerCase()));
            }
            case NUMBER_OF_PETS_BY_PET_CODES -> {
                resultList = ReportBeanUtil.mergePetReportDataByPetCode(resultList);
                resultList.sort(Comparator.comparing(PetReportRecord::getPetCode));
            }
            default -> {}
        }

        return resultList.stream().map(CommonUtil::transBean2Map).toList();
    }

    private List<Map<String, Object>> queryProductReport(
            List<Long> businessIds, Integer reportId, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return List.of();
        }

        List<ReportRecordByBusinessId> resultList = businessIds.stream()
                .map(businessId -> CompletableFuture.supplyAsync(() -> reportService.queryProductReportByBusinessId(
                        businessId.intValue(), reportId, startDate, endDate)))
                .map(CompletableFuture::join)
                .sorted(Comparator.comparing(ReportRecordByBusinessId::getBusinessId))
                .toList();

        return resultList.stream()
                .map(ReportRecordByBusinessId::getReportRecordList)
                .flatMap(Collection::stream)
                .toList();
    }

    public ReportTrendDTO getReportTrend(QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        boolean isMigrate = migrateHelper.isMigrate(tokenCompanyId);
        List<Long> businessIds = isMigrate ? checkAndGetBusinessIds(params) : List.of(businessId.longValue());

        if (businessIds.size() == 1) {
            // 传一个 businessId 时，走老的逻辑
            return reportService.getReportTrend(businessIds.get(0).intValue(), startDate, endDate);
        }

        List<ReportTrendDataDTO> reportTrendDataList = businessIds.stream()
                .map(bId -> CompletableFuture.supplyAsync(
                        () -> groomingReportClient.getReportTrendData(new GetReportTrendParams()
                                .setCompanyId(tokenCompanyId)
                                .setBusinessId(bId)
                                .setStartDate(startDate)
                                .setEndDate(endDate))))
                .map(CompletableFuture::join)
                .toList();
        return ReportBeanUtil.mergeReportTrendData(reportTrendDataList);
    }

    public List<ReportCollectedRevenueDTO> getCollectedRevReport(Integer staffId, QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        boolean isMigrate = migrateHelper.isMigrate(tokenCompanyId);
        List<Long> businessIds = isMigrate ? checkAndGetBusinessIds(params) : List.of(businessId.longValue());

        if (businessIds.size() == 1) {
            // 传一个 businessId 时，走老的逻辑
            return reportService.getCollectedRevReport(
                    tokenCompanyId, businessIds.get(0).intValue(), staffId, startDate, endDate);
        }

        List<ReportCollectedRevenueDTO> reportDataList = businessIds.stream()
                .map(bId -> CompletableFuture.supplyAsync(
                        () -> groomingReportClient.getCollectedRevReport(bId.intValue(), staffId, startDate, endDate)))
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .toList();

        List<Integer> staffIds = reportDataList.stream()
                .map(ReportCollectedRevenueDTO::getStaffId)
                .distinct()
                .toList();
        Map<Integer, MoeStaffDto> staffMap = staffService.getStaffMapByIds(tokenCompanyId, staffIds);
        return ReportBeanUtil.mergeCollectedRevenue(reportDataList, staffMap);
    }

    public List<ReportExpectedRevenueDTO> getExpectedRevReport(Integer staffId, QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        boolean isMigrate = migrateHelper.isMigrate(tokenCompanyId);
        List<Long> businessIds = isMigrate ? checkAndGetBusinessIds(params) : List.of(businessId.longValue());

        if (businessIds.size() == 1) {
            // 传一个 businessId 时，走老的逻辑
            return reportService.getExpectedRevReport(
                    tokenCompanyId, businessIds.get(0).intValue(), staffId, startDate, endDate);
        }

        List<ReportExpectedRevenueDTO> reportDataList = businessIds.stream()
                .map(bId -> CompletableFuture.supplyAsync(
                        () -> groomingReportClient.getExpectedRevReport(bId.intValue(), staffId, startDate, endDate)))
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .toList();

        List<Integer> staffIds = reportDataList.stream()
                .map(ReportExpectedRevenueDTO::getStaffId)
                .distinct()
                .toList();
        Map<Integer, MoeStaffDto> staffMap = staffService.getStaffMapByIds(tokenCompanyId, staffIds);
        return ReportBeanUtil.mergeExpectedRevenue(reportDataList, staffMap);
    }

    public List<ReportPaymentSummaryDto> getPaymentReport(Integer staffId, QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        boolean isMigrate = migrateHelper.isMigrate(tokenCompanyId);
        List<Long> businessIds = isMigrate ? checkAndGetBusinessIds(params) : List.of(businessId.longValue());

        if (businessIds.size() == 1) {
            // 传一个 businessId 时，走老的逻辑
            return reportService.getPaymentReport(businessIds.get(0).intValue(), staffId, startDate, endDate);
        }

        List<ReportPaymentSummaryDto> reportDataList = businessIds.stream()
                .map(bId -> CompletableFuture.supplyAsync(
                        () -> groomingReportClient.getPaymentReport(bId.intValue(), staffId, startDate, endDate)))
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .toList();

        return ReportBeanUtil.mergePaymentSummaryReport(reportDataList);
    }

    /**
     * 根据businessId 和 雇佣日期 获取staff列表 ,包含已删除的staff
     *
     * @param params startDate, endDate, businessIds
     * @return staff列表
     */
    public List<MoeStaff> getStaffsByBusinessIdAndHireDate(QueryReportParams params) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        List<Long> businessIds = migrateInfo.isMigrate() ? params.getBusinessIds() : List.of(businessId.longValue());

        if (CollectionUtils.isEmpty(businessIds) || businessIds.size() == 1) {
            // 传一个 businessId 或者不传时，走老的逻辑
            Integer bId = CollectionUtils.isEmpty(businessIds)
                    ? businessId
                    : businessIds.get(0).intValue();
            return getStaffsByBusinessIdAndHireDate(bId, startDate, endDate, migrateInfo);
        }

        List<Long> companyBusinessIds = businessMapper.getBusinessByCompanyId(tokenCompanyId.intValue()).stream()
                .map(MoeBusiness::getId)
                .map(Integer::longValue)
                .toList();
        businessIds.retainAll(companyBusinessIds);

        List<MoeStaff> staffs = businessIds.stream()
                .map(bId -> CompletableFuture.supplyAsync(
                        () -> getStaffsByBusinessIdAndHireDate(bId.intValue(), startDate, endDate, migrateInfo)))
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MoeStaff::getId))),
                        ArrayList::new));

        staffs.sort(Comparator.comparing(MoeStaff::getSort).reversed().thenComparing(MoeStaff::getId));
        return staffs;
    }

    public List<MoeStaff> getStaffsByBusinessIdAndHireDate(
            Integer businessId, String startDate, String endDate, MigrateInfo migrateInfo) {
        DateRangeParams dateRangeParams = new DateRangeParams();
        dateRangeParams.setStartDate(startDate);
        dateRangeParams.setEndDate(endDate);
        Set<Integer> staffIdSet = groomingAppointmentClient.queryStaffIdListByDateRange(businessId, dateRangeParams);

        List<MoeStaff> staffs = staffService.getStaffListByBusinessId(businessId, false, migrateInfo);
        staffIdSet.removeIf(
                staffId -> staffs.stream().anyMatch(staff -> staff.getId().equals(staffId)));
        if (CollectionUtils.isEmpty(staffIdSet)) {
            staffs.sort(Comparator.comparing(MoeStaff::getSort).reversed());
            return staffs;
        }
        staffs.addAll(staffService.getStaffByIds(migrateInfo.companyId(), new ArrayList<>(staffIdSet)));
        staffs.sort(Comparator.comparing(MoeStaff::getSort).reversed());
        return staffs;
    }
}
