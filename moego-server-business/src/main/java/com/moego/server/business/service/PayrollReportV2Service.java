package com.moego.server.business.service;

import static com.moego.server.business.service.util.ReportBeanUtil.buildName;
import static com.moego.server.business.service.util.ReportBeanUtil.fillStaffNameForPayrollData;

import com.moego.common.dto.PageDTO;
import com.moego.common.enums.PayrollConst;
import com.moego.common.enums.ReportConst;
import com.moego.common.enums.StaffEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.consts.report.AccessReportPermissionScopeEnum;
import com.moego.server.business.consts.report.ReportFieldsFactory;
import com.moego.server.business.converter.PayrollDataConverter;
import com.moego.server.business.dto.BusinessPayrollSettingDTO;
import com.moego.server.business.dto.PayrollEmployeeDTO;
import com.moego.server.business.dto.PayrollEmployeeDetailDTO;
import com.moego.server.business.dto.StaffPayrollSettingDTO;
import com.moego.server.business.mapper.MoeBusinessClockInOutLogMapper;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.PayrollStaffDetailVO;
import com.moego.server.business.service.dto.ClockInOutLogCountDTO;
import com.moego.server.business.service.dto.ClockInOutLogDto;
import com.moego.server.business.service.dto.PayrollExportableDTO;
import com.moego.server.business.service.dto.export.ExportPayrollDataSummary;
import com.moego.server.business.service.dto.export.ExportPayrollStaffOverviewData;
import com.moego.server.business.service.dto.report.PayrollReportData;
import com.moego.server.business.service.params.PayrollExportParams;
import com.moego.server.business.service.params.PayrollExportableParams;
import com.moego.server.business.service.util.ReportBeanUtil;
import com.moego.server.business.web.param.QueryReportParams;
import com.moego.server.business.web.vo.report.EmployeePayrollRecord;
import com.moego.server.business.web.vo.report.EmployeeReportRecord;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.client.IGroomingReportClient;
import com.moego.server.grooming.dto.report.PayrollReportCountV2DTO;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.params.report.GetReportParams;
import com.moego.server.grooming.params.report.QueryPayrollReportByPageParams;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class PayrollReportV2Service {

    private static final String defaultExportFileName = "MoeGo Payroll(%s - %s)";

    @Autowired
    private MoeBusinessClockInOutLogMapper clockInOutMapper;

    @Autowired
    private ExportService exportService;

    @Autowired
    private PayrollReportService payrollReportService;

    @Autowired
    private PayrollSettingService payrollSettingService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private ICustomerCustomerClient customerCustomerClient;

    @Autowired
    private IGroomingReportClient groomingReportClient;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private CompanyService companyService;

    private List<Long> checkAndGetBusinessIds(QueryReportParams params) {
        Boolean isAllLocation = params.getIsAllLocation();
        List<Long> businessIds = params.getBusinessIds();
        // payroll 不管是什么权限都需要取全部 locations
        List<Long> allLocationIds = businessService.getBusinessIdsByCompanyId(params.getTokenCompanyId()).stream()
                .map(Integer::longValue)
                .toList();
        if (Boolean.TRUE.equals(isAllLocation)) {
            return allLocationIds;
        }
        if (CollectionUtils.isEmpty(businessIds)) {
            // businessIds 为空，且 isAllLocation 为 false，报错
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "IsAllLocation and businessIds is empty");
        }
        businessIds.retainAll(allLocationIds);
        if (CollectionUtils.isEmpty(businessIds)) {
            // 传了异常 businessId
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Business id error");
        }
        return businessIds;
    }

    private boolean isOnlyTheirOwn(Long companyId, Long staffId) {
        Map<PermissionEnums, Long> permissionMap = permissionHelper.getPermissionMapByStaffId(companyId, staffId);
        if (permissionMap.get(PermissionEnums.ACCESS_PAYROLL_REPORT) == null) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "No permission to access report");
        }
        return permissionMap.get(PermissionEnums.ACCESS_PAYROLL_REPORT)
                == AccessReportPermissionScopeEnum.OF_THEIR_OWN.getValue();
    }

    public List<PayrollEmployeeDTO> getStaffPayrollList(QueryReportParams params) {
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();

        boolean isOnlyTheirOwn = isOnlyTheirOwn(tokenCompanyId, params.getTokenStaffId());
        List<Long> businessIds = checkAndGetBusinessIds(params);
        if (businessIds.size() == 1) {
            // 迁移前或只有一个 businessId 时走老逻辑
            params.setBusinessId(businessIds.get(0).intValue());
            return payrollReportService.getStaffPayrollList(params, isOnlyTheirOwn);
        }

        // 查询 staff payroll setting
        Map<Integer, StaffPayrollSettingDTO> staffPayrollSettingMap =
                payrollReportService.getStaffPayrollSettingMap(tokenCompanyId);
        List<PayrollReportData> reportDataList = businessIds.stream()
                .map(businessId -> CompletableFuture.supplyAsync(() -> payrollReportService.queryPayrollReportData(
                        businessId.intValue(), startDate, endDate, staffPayrollSettingMap)))
                .map(CompletableFuture::join)
                .toList();

        // 合并数据
        Map<Integer, ReportWebEmployee> apptReportMap = ReportBeanUtil.mergePayrollApptReports(reportDataList);
        Map<Integer, EmployeeReportRecord> clockReportMap = ReportBeanUtil.mergePayrollClockReports(reportDataList);
        // 查询整个 company 的 staff
        List<MoeStaff> staffs = staffService.getStaffListByCompanyId(tokenCompanyId, true);
        return staffs.stream()
                // 有数据或者非删除状态的员工
                .filter(s -> apptReportMap.containsKey(s.getId())
                        || clockReportMap.containsKey(s.getId())
                        || Objects.equals(s.getStatus(), StaffEnum.STATUS_NORMAL))
                .filter(s -> !isOnlyTheirOwn
                        || s.getId().equals(params.getTokenStaffId().intValue()))
                .map(staff -> ReportBeanUtil.buildPayrollEmployeeDTO(
                        staff,
                        apptReportMap.get(staff.getId()),
                        staffPayrollSettingMap.get(staff.getId()),
                        clockReportMap.get(staff.getId())))
                .toList();
    }

    public PayrollEmployeeDetailDTO getStaffPayrollDetailPage(PayrollStaffDetailVO params) {
        Integer tokenBusinessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();

        boolean isOnlyTheirOwn = isOnlyTheirOwn(tokenCompanyId, params.getTokenStaffId());
        if (isOnlyTheirOwn
                && !Objects.equals(params.getStaffId(), params.getTokenStaffId().intValue())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No permission to access this staff's payroll");
        }
        List<Long> businessIds = checkAndGetBusinessIds(new QueryReportParams()
                .setBusinessId(params.getBusinessId())
                .setTokenStaffId(params.getTokenStaffId())
                .setTokenCompanyId(tokenCompanyId)
                .setIsAllLocation(params.getIsAllLocation())
                .setBusinessIds(params.getBusinessIds()));
        if (businessIds.size() == 1) {
            // 迁移前或只有一个 businessId 时走老逻辑
            params.setBusinessId(businessIds.get(0).intValue());
            return payrollReportService.getStaffPayrollDetailPage(params);
        }

        List<EmployeePayrollRecord> records = null;
        Byte type = params.getType();
        // 查询 company preference setting
        var preferenceSetting = companyService.getCompanyPreferenceSetting(tokenCompanyId);
        long totalCount = 0L;
        if (params.getStaffId() != null) {
            // staff检查
            MoeStaff staff = staffService.getCompanyStaff(tokenCompanyId, params.getStaffId());
            if (staff == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "staff not found");
            }
            // 查询 staff payroll setting
            StaffPayrollSettingDTO staffPayrollSetting =
                    payrollSettingService.getStaffPayrollSetting(tokenCompanyId, tokenBusinessId, staff.getId());
            // business payroll setting
            BusinessPayrollSettingDTO businessPayrollSetting =
                    payrollSettingService.getBusinessPayrollSetting(tokenBusinessId);
            boolean isBasedOnCollected = businessPayrollSetting != null
                    && PayrollConst.COMMISSION_BASED_ACTUAL_PAYMENT.equals(
                            businessPayrollSetting.getServiceCommissionBased());

            // 分页参数检查
            if (Objects.isNull(params.getPageNum())) {
                params.setPageNum(1);
            }
            if (Objects.isNull(params.getPageSize())) {
                params.setPageSize(10);
            }

            if (!StringUtils.hasLength(params.getStartDate()) || !StringUtils.hasLength(params.getEndDate())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "start date or end date is empty");
            }

            switch (type) {
                case ReportConst.PAYROLL_TYPE_HOURLY -> {
                    boolean hourlyCommissionEnable =
                            Boolean.TRUE.equals(staffPayrollSetting.getHourlyCommissionEnable());
                    if (!hourlyCommissionEnable) {
                        break;
                    }
                    BigDecimal hourlyPay = staffPayrollSetting.getHourlyPay();
                    List<Integer> businessIdList =
                            businessIds.stream().map(Long::intValue).toList();
                    totalCount = clockInOutMapper.selectByClockDateCount(
                            businessIdList, params.getStaffId(), params.getStartDate(), params.getEndDate());
                    List<ClockInOutLogDto> clockLogs = clockInOutMapper.selectByClockDate(
                            businessIdList,
                            staff.getId(),
                            params.getStartDate(),
                            params.getEndDate(),
                            CommonUtil.getLimitOffset(params.getPageNum(), params.getPageSize()),
                            params.getPageSize());
                    records = payrollReportService.processHourlyCommission(clockLogs, preferenceSetting, hourlyPay);
                }
                case ReportConst.PAYROLL_TYPE_SERVICE, ReportConst.PAYROLL_TYPE_TIPS -> {
                    // 如果当前 commission disable，返回 null
                    boolean serviceCommissionEnable =
                            Boolean.TRUE.equals(staffPayrollSetting.getServiceCommissionEnable());
                    boolean tipsCommissionEnable = Boolean.TRUE.equals(staffPayrollSetting.getTipsCommissionEnable());
                    if ((type == ReportConst.PAYROLL_TYPE_SERVICE && !serviceCommissionEnable)
                            || (type == ReportConst.PAYROLL_TYPE_TIPS && !tipsCommissionEnable)) {
                        break;
                    }
                    PageDTO<ReportWebEmployee> queryPayrollResult =
                            groomingReportClient.queryPayrollReportByPageV2(new QueryPayrollReportByPageParams()
                                    .setBusinessIds(businessIds.stream()
                                            .map(Long::intValue)
                                            .toList())
                                    .setStaffId(params.getStaffId())
                                    .setType(type)
                                    .setStartDate(params.getStartDate())
                                    .setEndDate(params.getEndDate())
                                    .setPageNum(params.getPageNum())
                                    .setPageSize(params.getPageSize()));

                    // 查询 customerName
                    Map<Integer, String> customerNameMap = getCustomerNameMap(queryPayrollResult.getDataList());
                    totalCount = queryPayrollResult.getTotal();
                    records = queryPayrollResult.getDataList().stream()
                            .map(report -> {
                                if (type == ReportConst.PAYROLL_TYPE_SERVICE) {
                                    return ReportBeanUtil.buildServicePayrollReport(
                                            report,
                                            preferenceSetting,
                                            customerNameMap.getOrDefault(report.getCustomerId(), ""),
                                            isBasedOnCollected);
                                } else {
                                    return ReportBeanUtil.buildTipsPayrollReport(
                                            report,
                                            preferenceSetting,
                                            customerNameMap.getOrDefault(report.getCustomerId(), ""),
                                            isBasedOnCollected);
                                }
                            })
                            .collect(Collectors.toList());
                }
                default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "unknown type");
            }
        }

        // 使用type的int值来构建返回对象
        Integer typeIntVal = type.intValue();
        // 获取单位
        Map<String, String> originalUnits =
                ReportFieldsFactory.PAYROLL_TABLE_FIELD_UNIT_MAP.getOrDefault(typeIntVal, Map.of());
        Map<String, String> unitMap = new HashMap<>();
        String currencySymbol = preferenceSetting.getCurrencySymbol();
        if (originalUnits != null && originalUnits.containsValue("$")) {
            originalUnits.forEach((k, v) -> unitMap.put(k, v.equals("$") ? currencySymbol : v));
        }

        return PayrollEmployeeDetailDTO.builder()
                .type(typeIntVal)
                .title(ReportFieldsFactory.PAYROLL_TABLE_TITLE_MAP.get(typeIntVal))
                .fieldHeaders(ReportFieldsFactory.PAYROLL_TABLE_HEADERS_MAP.get(typeIntVal))
                .fieldKeys(ReportFieldsFactory.PAYROLL_TABLE_FIELD_KEY_MAP.get(typeIntVal))
                .fieldUnits(unitMap)
                .records(
                        records != null
                                ? records.stream()
                                        .map(CommonUtil::transBean2Map)
                                        .toList()
                                : null)
                .totalCount(totalCount)
                .build();
    }

    public List<PayrollExportableDTO> getStaffPayrollExportable(PayrollExportableParams params) {
        Long tokenCompanyId = params.getTokenCompanyId();
        Integer staffId = params.getStaffId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();

        boolean isOnlyTheirOwn = isOnlyTheirOwn(tokenCompanyId, params.getTokenStaffId());
        List<Long> businessIds = checkAndGetBusinessIds(params);
        if (businessIds.size() == 1) {
            // 迁移前或只有一个 businessId 时走老逻辑
            params.setBusinessId(businessIds.get(0).intValue());
            return payrollReportService.getStaffPayrollExportable(params, isOnlyTheirOwn);
        }

        List<Integer> staffIds;
        if (isOnlyTheirOwn) {
            if (staffId != null && !staffId.equals(params.getTokenStaffId().intValue())) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "No permission to access this staff's payroll");
            }
            staffIds = List.of(params.getTokenStaffId().intValue());
        } else {
            // 如果没有传 staffId，默认查询 business 全部 staff
            staffIds = params.getStaffId() != null
                    ? List.of(staffId)
                    : staffService.getStaffListByCompanyId(tokenCompanyId, true).stream()
                            .map(MoeStaff::getId)
                            .toList();
        }

        if (CollectionUtils.isEmpty(staffIds)) {
            return List.of();
        }
        var staffPayrollSettingMap = payrollReportService.getStaffPayrollSettingMap(tokenCompanyId);

        List<Integer> serviceEnableStaffIds = new ArrayList<>();
        List<Integer> tipsEnableStaffIds = new ArrayList<>();
        List<Integer> hourlyEnableStaffIds = new ArrayList<>();
        staffIds.forEach(sId -> {
            if (staffPayrollSettingMap.containsKey(sId)) {
                StaffPayrollSettingDTO setting = staffPayrollSettingMap.get(sId);
                if (Boolean.TRUE.equals(setting.getServiceCommissionEnable())) {
                    serviceEnableStaffIds.add(sId);
                }
                if (Boolean.TRUE.equals(setting.getTipsCommissionEnable())) {
                    tipsEnableStaffIds.add(sId);
                }
                if (Boolean.TRUE.equals(setting.getHourlyCommissionEnable())) {
                    hourlyEnableStaffIds.add(sId);
                }
            }
        });

        Set<Integer> serviceCommissionStaffIds = new HashSet<>();
        Set<Integer> tipsCommissionStaffIds = new HashSet<>();
        Set<Integer> hourlyCommissionStaffIds = new HashSet<>();

        businessIds.forEach(businessId -> {
            // 已经查出预约的 staffId 不用再查
            serviceEnableStaffIds.removeAll(serviceCommissionStaffIds);
            GetReportParams queryCountParams = new GetReportParams()
                    .setCompanyId(tokenCompanyId)
                    .setBusinessId(businessId.intValue())
                    .setStaffIds(serviceEnableStaffIds)
                    .setStartDate(startDate)
                    .setEndDate(endDate);
            if (!CollectionUtils.isEmpty(serviceEnableStaffIds)) {
                // 查询 service payroll 相关预约数量
                List<PayrollReportCountV2DTO> servicePayrollCounts =
                        groomingReportClient.getStaffServicePayrollReportCounts(queryCountParams);
                servicePayrollCounts.forEach(count -> {
                    if (count.payrollCount() > 0) {
                        serviceCommissionStaffIds.add(count.staffId());
                    }
                });
            }
            // 已经查出 tips 预约的 staffId 不用再查
            tipsEnableStaffIds.removeAll(tipsCommissionStaffIds);
            if (!CollectionUtils.isEmpty(tipsEnableStaffIds)) {
                queryCountParams.setStaffIds(tipsEnableStaffIds);
                List<PayrollReportCountV2DTO> tipsPayrollCounts =
                        groomingReportClient.getStaffTipsPayrollReportCounts(queryCountParams);
                tipsPayrollCounts.forEach(count -> {
                    if (count.payrollCount() > 0) {
                        tipsCommissionStaffIds.add(count.staffId());
                    }
                });
            }
            // 已经查出打卡记录的 staffId 不用再查
            hourlyEnableStaffIds.removeAll(hourlyCommissionStaffIds);
            if (!CollectionUtils.isEmpty(hourlyEnableStaffIds)) {
                List<ClockInOutLogCountDTO> staffClockInOutCount = clockInOutMapper.selectStaffCountsByClockDate(
                        businessId.intValue(), hourlyEnableStaffIds, startDate, endDate);
                staffClockInOutCount.forEach(count -> {
                    if (count.getCount() > 0) {
                        hourlyCommissionStaffIds.add(count.getStaffId());
                    }
                });
            }
        });

        return staffIds.parallelStream()
                .map(sId -> {
                    PayrollExportableDTO exportableDTO = new PayrollExportableDTO()
                            .setStaffId(sId)
                            .setServiceCommission(false)
                            .setTipsCommission(false)
                            .setHourlyPay(false);
                    StaffPayrollSettingDTO payrollSetting = staffPayrollSettingMap.get(sId);
                    if (payrollSetting == null) {
                        return exportableDTO;
                    }
                    // 有数据时，对应 exportable 字段设置为true
                    if (payrollSetting.getServiceCommissionEnable() && serviceCommissionStaffIds.contains(sId)) {
                        exportableDTO.setServiceCommission(true);
                    }
                    if (payrollSetting.getTipsCommissionEnable() && tipsCommissionStaffIds.contains(sId)) {
                        exportableDTO.setTipsCommission(true);
                    }
                    if (payrollSetting.getHourlyCommissionEnable() && hourlyCommissionStaffIds.contains(sId)) {
                        exportableDTO.setHourlyPay(true);
                    }
                    return exportableDTO;
                })
                .collect(Collectors.toList());
    }

    public String getStaffPayrollExportUrl(PayrollExportParams params) {
        Long tokenCompanyId = params.getTokenCompanyId();

        boolean isOnlyTheirOwn = isOnlyTheirOwn(tokenCompanyId, params.getTokenStaffId());
        List<Long> businessIds = checkAndGetBusinessIds(params);
        if (businessIds.size() == 1) {
            // 迁移前走老逻辑
            params.setBusinessId(businessIds.get(0).intValue());
            return payrollReportService.getStaffPayrollExportUrl(params, isOnlyTheirOwn);
        }

        // 默认导出全部，导出时如无数据会跳过对应的 type
        if (params.getContentType() == null || params.getContentType().length == 0) {
            params.setContentType(new Byte[] {
                ReportConst.PAYROLL_TYPE_SERVICE, ReportConst.PAYROLL_TYPE_HOURLY, ReportConst.PAYROLL_TYPE_TIPS,
            });
        }

        List<Integer> staffIds = params.getStaffId() != null
                ? List.of(params.getStaffId())
                : staffService.getStaffListByCompanyId(tokenCompanyId, true).stream()
                        .map(MoeStaff::getId)
                        .toList();
        if (CollectionUtils.isEmpty(staffIds)) {
            return null;
        }

        // 导出单个 staff 为 xlsx 文件，多个 staff 为 zip 文件
        if (staffIds.size() == 1) {
            params.setStaffId(staffIds.get(0));
            return exportPayrollReportForOneStaff(businessIds, params);
        } else {
            return exportPayrollReportForStaffs(businessIds, staffIds, params);
        }
    }

    /**
     * 导出一个 staff 的 Payroll 明细
     *
     * @param params                 导出参数
     * @return 导出文件 url
     */
    private String exportPayrollReportForOneStaff(List<Long> businessIds, PayrollExportParams params) {
        Integer staffId = params.getStaffId();
        Long companyId = params.getTokenCompanyId();

        MoeStaff staff = staffService.getCompanyStaff(companyId, staffId);
        if (staff == null) {
            return null;
        }
        // 查询 company preference setting
        var preferenceSetting = companyService.getCompanyPreferenceSetting(companyId);

        ExportPayrollDataSummary exportData = getExportDataForOneStaff(businessIds, preferenceSetting, staff, params);
        fillStaffNameForPayrollData(exportData, buildName(staff.getFirstName(), staff.getLastName()));
        if (exportData == null) {
            return null;
        }
        String fileName = StringUtils.hasText(params.getFileName())
                ? params.getFileName()
                : getDefaultExportFileName(
                        staff, params.getStartDate(), params.getEndDate(), ExportService.XLSX_FILE_SUFFIX);
        return exportService.uploadExportPayrollFile(exportData, fileName);
    }

    private ExportPayrollDataSummary getExportDataForOneStaff(
            List<Long> businessIds,
            CompanyPreferenceSettingModel preferenceSetting,
            MoeStaff staff,
            PayrollExportParams params) {
        List<ExportPayrollDataSummary> exportDataList = businessIds.stream()
                .map(businessId -> CompletableFuture.supplyAsync(() -> {
                    // 查询 staff payroll setting
                    StaffPayrollSettingDTO staffPayrollSetting = payrollSettingService.getStaffPayrollSetting(
                            staff.getCompanyId().longValue(), businessId.intValue(), staff.getId());
                    BusinessPayrollSettingDTO businessPayrollSetting =
                            payrollSettingService.getBusinessPayrollSetting(businessId.intValue());
                    return payrollReportService.getStaffPayrollExportData(
                            businessId.intValue(),
                            preferenceSetting,
                            staff,
                            params,
                            businessPayrollSetting,
                            staffPayrollSetting);
                }))
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .toList();

        return ReportBeanUtil.mergeExportPayrollDataSummary(exportDataList);
    }

    private String exportPayrollReportForStaffs(
            List<Long> businessIds, List<Integer> staffIds, PayrollExportParams params) {
        Long tokenCompanyId = params.getTokenCompanyId();
        List<MoeStaff> staffs = staffService.getStaffByIds(tokenCompanyId, staffIds);
        if (CollectionUtils.isEmpty(staffs) || CollectionUtils.isEmpty(businessIds)) {
            return null;
        }
        // 查询 company preference setting
        var preferenceSetting = companyService.getCompanyPreferenceSetting(tokenCompanyId);

        Map<Integer, ExportPayrollDataSummary> exportDataMap = new ConcurrentHashMap<>();
        List<ExportPayrollStaffOverviewData> staffPayrollOverviews = new ArrayList<>();

        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        for (MoeStaff staff : staffs) {
            completableFutureList.add(CompletableFuture.runAsync(
                    () -> {
                        ExportPayrollDataSummary exportData =
                                getExportDataForOneStaff(businessIds, preferenceSetting, staff, params);
                        if (exportData == null) {
                            return;
                        }
                        fillStaffNameForPayrollData(exportData, buildName(staff.getFirstName(), staff.getLastName()));
                        exportDataMap.put(staff.getId(), exportData);
                    },
                    ThreadPool.getExecuteExecutor()));
        }
        completableFutureList.add(CompletableFuture.runAsync(
                () -> {
                    // 查询 staff overview
                    staffPayrollOverviews.addAll(getStaffPayrollList(params).stream()
                            .map(PayrollDataConverter.INSTANCE::toOverviewData)
                            .toList());
                },
                ThreadPool.getExecuteExecutor()));

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .join();
        return payrollReportService.exportPayrollReportForStaffs(staffs, params, exportDataMap, staffPayrollOverviews);
    }

    /**
     * 生成 export fileName
     *
     * @param staff     staff 信息
     * @param startDate 导出查询范围开始日期
     * @param endDate   导出查询范围结束日期
     * @param suffix    文件后缀
     * @return 文件名
     */
    private String getDefaultExportFileName(MoeStaff staff, String startDate, String endDate, String suffix) {
        if (staff != null) {
            return (buildName(staff.getFirstName(), staff.getLastName()) + " "
                    + String.format(defaultExportFileName, startDate, endDate)
                    + suffix);
        } else {
            return String.format(defaultExportFileName, startDate, endDate) + suffix;
        }
    }

    private Map<Integer, String> getCustomerNameMap(List<ReportWebEmployee> employees) {
        Set<Integer> customerIds =
                employees.stream().map(ReportWebEmployee::getCustomerId).collect(Collectors.toSet());
        CustomerIdListParams queryParams = new CustomerIdListParams();
        queryParams.setIdList(new ArrayList<>(customerIds));
        List<MoeBusinessCustomerDTO> customers = customerCustomerClient.queryCustomerListWithDeleted(queryParams);
        return customers.stream()
                .collect(Collectors.toMap(
                        MoeBusinessCustomerDTO::getCustomerId, c -> buildName(c.getFirstName(), c.getLastName())));
    }
}
