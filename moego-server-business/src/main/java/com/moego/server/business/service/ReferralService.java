package com.moego.server.business.service;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.BusinessReferralConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.config.ReferralConfig;
import com.moego.server.business.dto.ReferralInfoDTO;
import com.moego.server.business.dto.ReferralStatusDTO;
import com.moego.server.business.mapper.MoeCompanyMapper;
import com.moego.server.business.mapper.MoeReferralBonusRewardRuleMapper;
import com.moego.server.business.mapper.MoeReferralFixedRewardRuleMapper;
import com.moego.server.business.mapper.MoeReferralInfoMapper;
import com.moego.server.business.mapper.MoeReferralRewardRecordMapper;
import com.moego.server.business.mapperbean.MoeCompany;
import com.moego.server.business.mapperbean.MoeReferralBonusRewardRule;
import com.moego.server.business.mapperbean.MoeReferralFixedRewardRule;
import com.moego.server.business.mapperbean.MoeReferralInfo;
import com.moego.server.business.mapperbean.MoeReferralRewardRecord;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.business.params.ReferralInfoParams;
import com.moego.server.business.service.dto.referral.ReferralCountDTO;
import com.moego.server.business.service.dto.referral.ReferralUpdateStatusDTO;
import com.moego.server.business.service.params.ReferralBonusParams;
import com.moego.server.business.web.vo.referral.ReferralBonusRewardVO;
import com.moego.server.business.web.vo.referral.ReferralInfoVO;
import com.moego.server.business.web.vo.referral.ReferralItemInfoVO;
import com.moego.server.payment.client.IPaymentStripeClient;
import com.moego.server.payment.client.IPaymentSubscriptionClient;
import com.moego.server.payment.dto.CompanyPermissionStateDto;
import com.moego.server.payment.dto.StripeCustomerDTO;
import com.moego.server.payment.dto.StripeInvoiceCountDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/6/30
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReferralService {

    private final AccountService accountService;

    private final MoeReferralInfoMapper referralInfoMapper;

    private final ReferralRewardService referralRewardService;

    private final IPaymentSubscriptionClient paymentSubscriptionClient;

    private final IPaymentStripeClient paymentStripeClient;

    private final MoeCompanyMapper companyMapper;

    private final MoeReferralFixedRewardRuleMapper fixedRewardRuleMapper;

    private final MoeReferralBonusRewardRuleMapper bonusRewardRuleMapper;

    private final ReferralConfig referralConfig;

    private final ReminderService reminderService;

    private final ReferralRuleService referralRuleService;

    private final ReferralSyncService referralSyncService;

    private final MoeReferralRewardRecordMapper referralRewardRecordMapper;

    private final LockManager lockManager;

    public String generateReferralCode() {
        String code;
        MoeReferralInfo referralInfo;
        do {
            code = BusinessReferralConst.CODE_PREFIX + CommonUtil.getRandomIntUpperString(4);
            referralInfo = referralInfoMapper.selectByReferralCode(code);
        } while (referralInfo != null);
        return code;
    }

    public void bindingReferral(MoeReferralInfo refereeInfo, String referralCode) {
        if (StringUtils.isBlank(referralCode) || !Objects.equals(refereeInfo.getReferralId(), 0)) {
            return;
        }
        // binding
        MoeReferralInfo referralInfo = referralInfoMapper.selectByReferralCode(referralCode);
        if (Objects.isNull(referralInfo)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Illegal Referral Code");
        }
        refereeInfo.setReferralId(referralInfo.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer addReferralInfo(ReferralInfoParams referralParams) {
        if (Objects.isNull(referralParams.getAccountId()) || Objects.isNull(referralParams.getCompanyId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "accountId or companyId is null");
        }
        if (Objects.isNull(referralParams.getStatus())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "status is null");
        }
        String companyAccount = String.valueOf(referralParams.getCompanyId()) + referralParams.getAccountId();
        String resourceKey = lockManager.getResourceKey(LockManager.BUSINESS_REFERRAL, companyAccount);
        String value = CommonUtil.getUuid();
        try {
            if (lockManager.lockWithRetry(resourceKey, value)) {
                // double check
                MoeReferralInfo referralInfo = referralInfoMapper.selectByCompanyIdAndAccountId(
                        referralParams.getCompanyId(), referralParams.getAccountId());
                if (Objects.nonNull(referralInfo)) {
                    return referralInfo.getId();
                }
                MoeReferralInfo refereeInfo = new MoeReferralInfo();
                refereeInfo.setAccountId(referralParams.getAccountId());
                refereeInfo.setCompanyId(referralParams.getCompanyId());
                refereeInfo.setReferralCode(generateReferralCode());
                refereeInfo.setStatus(referralParams.getStatus());
                Long now = DateUtil.get10Timestamp();
                refereeInfo.setCreateTime(now);
                refereeInfo.setUpdateTime(now);
                bindingReferral(refereeInfo, referralParams.getReferralCode());
                referralInfoMapper.insertSelective(refereeInfo);
                return refereeInfo.getId();
            } else {
                throw new CommonException(ResponseCodeEnum.PARALLEL_ERROR, "get lock failed for " + resourceKey);
            }
        } finally {
            lockManager.unlock(resourceKey, value);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateReferralInfo(ReferralInfoParams referralParams) {
        MoeReferralInfo refereeInfo = referralInfoMapper.selectByCompanyIdAndAccountId(
                referralParams.getCompanyId(), referralParams.getAccountId());
        // initialize referral info
        if (Objects.isNull(refereeInfo)) {
            addReferralInfo(referralParams);
            return;
        }
        MoeReferralInfo updateInfo = new MoeReferralInfo();
        updateInfo.setId(refereeInfo.getId());
        updateInfo.setUpdateTime(DateUtil.get10Timestamp());
        updateInfo.setStatus(referralParams.getStatus());
        if (StringUtils.isNotEmpty(referralParams.getReferralCode())) {
            bindingReferral(refereeInfo, referralParams.getReferralCode());
            updateInfo.setReferralId(refereeInfo.getReferralId());
        }
        referralInfoMapper.updateByPrimaryKeySelective(updateInfo);
    }

    public List<ReferralItemInfoVO> getReferralList(Integer referralId) {
        List<MoeReferralInfo> referralInfoList = referralInfoMapper.getReferralListByReferralId(referralId);
        if (CollectionUtils.isEmpty(referralInfoList)) {
            return Collections.emptyList();
        }
        List<Integer> companyIdList =
                referralInfoList.stream().map(MoeReferralInfo::getCompanyId).collect(Collectors.toList());
        CommonIdsParams commonIdsParams = new CommonIdsParams();
        commonIdsParams.setIds(companyIdList);
        List<CompanyPermissionStateDto> permissionStateDtoList =
                paymentSubscriptionClient.getPermissionStateByCompanyIdList(commonIdsParams);
        Map<Integer, CompanyPermissionStateDto> permissionStateDtoMap = permissionStateDtoList.stream()
                .collect(Collectors.toMap(CompanyPermissionStateDto::getCompanyId, Function.identity()));
        List<StripeInvoiceCountDTO> countDTOList = paymentStripeClient.countPaidInvoiceByCompanyIdList(companyIdList);
        Map<Integer, Integer> companyChargeCountMap = countDTOList.stream()
                .collect(Collectors.toMap(StripeInvoiceCountDTO::getCompanyId, StripeInvoiceCountDTO::getChargeCount));
        // company owner account id
        List<Integer> accountIdList =
                referralInfoList.stream().map(MoeReferralInfo::getAccountId).collect(Collectors.toList());
        Map<Integer, AccountModel> accountMap = accountService.getAccountByIdList(accountIdList);
        return referralInfoList.stream()
                .map(itemInfo -> {
                    ReferralItemInfoVO itemInfoVO = new ReferralItemInfoVO();
                    BeanUtils.copyProperties(itemInfo, itemInfoVO);
                    AccountModel account = accountMap.get(itemInfo.getAccountId());
                    if (Objects.nonNull(account)) {
                        itemInfoVO.setFirstName(account.getFirstName());
                        itemInfoVO.setLastName(account.getLastName());
                        itemInfoVO.setAvatarPath(account.getAvatarPath());
                    }
                    if (Objects.equals(itemInfo.getStatus(), BusinessReferralConst.REFERRAL_UPGRADED)) {
                        CompanyPermissionStateDto permissionStateDto =
                                permissionStateDtoMap.get(itemInfo.getCompanyId());
                        Integer successDayThreshold = DateUtil.minute2UpperDay(referralConfig.getSuccessMinThreshold());
                        if (Objects.isNull(permissionStateDto)) {
                            log.error(
                                    "upgraded company don't have permission state info, companyId: {}",
                                    itemInfo.getCompanyId());
                            itemInfoVO.setRemainDays(successDayThreshold);
                            return itemInfoVO;
                        }
                        int upgradedDays = Long.valueOf(
                                        (DateUtil.get10Timestamp() - permissionStateDto.getBeginDate()) / 86400)
                                .intValue();
                        itemInfoVO.setRemainDays(
                                successDayThreshold > upgradedDays ? successDayThreshold - upgradedDays : 1);
                    } else if (Objects.equals(itemInfo.getStatus(), BusinessReferralConst.REFERRAL_SUCCESS)) {
                        Integer chargeCount = companyChargeCountMap.getOrDefault(itemInfo.getCompanyId(), 0);
                        itemInfoVO.setChargeCount(chargeCount);
                    }
                    return itemInfoVO;
                })
                .collect(Collectors.toList());
    }

    public MoeReferralInfo initializeReferralInfo(ReferralInfoParams referralInfoParams) {
        CompanyPermissionStateDto permissionStateDto =
                paymentSubscriptionClient.getPermissionStateByCompanyId(referralInfoParams.getCompanyId());
        boolean isSubscribing =
                Objects.nonNull(permissionStateDto) && !Objects.equals(permissionStateDto.getLevel(), 0);
        referralInfoParams.setStatus(
                isSubscribing ? BusinessReferralConst.REFERRAL_UPGRADED : BusinessReferralConst.REFERRAL_SIGNED_UP);
        Integer id = addReferralInfo(referralInfoParams);
        return referralInfoMapper.selectByPrimaryKey(id);
    }

    public ReferralInfoVO getRefererInfo(ReferralInfoParams referralInfoParams) {
        MoeReferralInfo refererInfo = referralInfoMapper.selectByCompanyIdAndAccountId(
                referralInfoParams.getCompanyId(), referralInfoParams.getAccountId());
        ReferralInfoVO refererInfoVO = new ReferralInfoVO();
        // set config params
        List<MoeReferralFixedRewardRule> fixedRewardRuleList = referralRuleService.getActiveFixedRule();
        if (fixedRewardRuleList.size() >= 2) {
            refererInfoVO.setFirstFixedReward(fixedRewardRuleList.get(0).getFixedAmount());
            refererInfoVO.setFixedReward(
                    fixedRewardRuleList.get(fixedRewardRuleList.size() - 1).getFixedAmount());
        } else {
            refererInfoVO.setFirstFixedReward(BigDecimal.ZERO);
            refererInfoVO.setFixedReward(BigDecimal.ZERO);
        }
        refererInfoVO.setUpgradePercentOff(referralConfig.getRewardPercentOff());
        // initialize
        if (Objects.isNull(refererInfo)) {
            MoeReferralInfo referralInfo = initializeReferralInfo(referralInfoParams);
            BeanUtils.copyProperties(referralInfo, refererInfoVO);
            refererInfoVO.setLastEarning(BigDecimal.ZERO);
            refererInfoVO.setReferralList(Collections.emptyList());
            refererInfoVO.setBonusList(Collections.emptyList());
            return refererInfoVO;
        }
        BeanUtils.copyProperties(refererInfo, refererInfoVO);
        // get stripe balance
        StripeCustomerDTO stripeCustomerBalance =
                paymentStripeClient.getStripeCustomerBalance(referralInfoParams.getCompanyId());
        if (!stripeCustomerBalance.isNull()
                && Objects.nonNull(stripeCustomerBalance.getBalance())
                && stripeCustomerBalance.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            refererInfoVO.setBalance(stripeCustomerBalance.getBalance().negate().scaleByPowerOfTen(-2));
        } else if (refererInfo.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            refererInfoVO.setBalance(refererInfo.getBalance().negate());
        }
        BigDecimal earnReward = referralRewardService.getEarnRewardAndChecked(referralInfoParams.getCompanyId());
        refererInfoVO.setLastEarning(refererInfoVO.getTotalEarning().subtract(earnReward));
        // get referral list
        List<ReferralItemInfoVO> referralList = getReferralList(refererInfo.getId());
        refererInfoVO.setReferralList(referralList);
        // get bonus reward list
        List<ReferralBonusRewardVO> bonusRewardList = referralRewardService.getBonusRewardList(
                ReferralBonusParams.builder().referralId(refererInfo.getId()).build());
        refererInfoVO.setBonusList(bonusRewardList);
        return refererInfoVO;
    }

    public ReferralInfoDTO getRefererInfo(Integer companyId, Integer accountId) {
        MoeReferralInfo referralInfo = referralInfoMapper.selectByCompanyIdAndAccountId(companyId, accountId);
        ReferralInfoDTO referralInfoDTO = new ReferralInfoDTO();
        if (Objects.isNull(referralInfo)) {
            referralInfoDTO.setBalance(BigDecimal.ZERO);
            return referralInfoDTO;
        }
        BeanUtils.copyProperties(referralInfo, referralInfoDTO);
        return referralInfoDTO;
    }

    /**
     * get referee info map by status
     *
     * @param status {@link BusinessReferralConst}
     * @return companyId - referee info
     */
    public Map<Integer, MoeReferralInfo> getRefereeInfoMap(Byte status, List<Integer> companyIdList) {
        List<MoeReferralInfo> refereeList = referralInfoMapper.getReferralListByStatus(status, companyIdList);
        Set<Integer> companyIdSet = refereeList.stream()
                .map(MoeReferralInfo::getCompanyId)
                .filter(companyId -> !Objects.equals(companyId, 0))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(companyIdSet)) {
            return Collections.emptyMap();
        }
        List<MoeCompany> companyList = companyMapper.getCompanyByIdList(new ArrayList<>(companyIdSet));
        Map<Integer, Integer> companyOwnerMap =
                companyList.stream().collect(Collectors.toMap(MoeCompany::getId, MoeCompany::getAccountId));
        Map<Integer, List<MoeReferralInfo>> companyReferralListMap =
                refereeList.stream().collect(Collectors.groupingBy(MoeReferralInfo::getCompanyId));
        Map<Integer, MoeReferralInfo> companyReferralMap = new HashMap<>();
        companyReferralListMap.forEach((companyId, referralInfoList) -> {
            Integer ownerAccountId = companyOwnerMap.get(companyId);
            for (MoeReferralInfo referralInfo : referralInfoList) {
                // check company owner
                if (Objects.equals(referralInfo.getAccountId(), ownerAccountId)) {
                    companyReferralMap.put(companyId, referralInfo);
                    break;
                }
            }
        });
        return companyReferralMap;
    }

    /**
     * get referer info Map
     *
     * @param idList idList
     * @return id - referer info
     */
    public Map<Integer, MoeReferralInfo> getRefererInfoMap(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        List<MoeReferralInfo> referralInfoList = referralInfoMapper.getReferralListByIdList(idList);
        return referralInfoList.stream().collect(Collectors.toMap(MoeReferralInfo::getId, Function.identity()));
    }

    /**
     * count referralList by referralId
     *
     * @param referralIdList referral_id
     * @param status         {@link BusinessReferralConst}
     * @return referral_id - count
     */
    public Map<Integer, AtomicInteger> getReferralStatusCountMap(List<Integer> referralIdList, Byte status) {
        // earn fixed reward referee status in (2,3,-2)
        List<ReferralCountDTO> referralCountDTOList = referralInfoMapper.countGroupByReferralId(referralIdList, status);
        Map<Integer, ReferralCountDTO> refererCountMap = referralCountDTOList.stream()
                .collect(Collectors.toMap(ReferralCountDTO::getReferralId, Function.identity()));
        return referralIdList.stream().collect(Collectors.toMap(referralId -> referralId, referralId -> {
            ReferralCountDTO countDTO = refererCountMap.get(referralId);
            if (Objects.isNull(countDTO)) {
                return new AtomicInteger(0);
            }
            return new AtomicInteger(countDTO.getCount());
        }));
    }

    public void syncUpgradedToSuccess() {
        this.syncUpgradedToSuccess(Collections.emptyList());
    }

    /**
     * get upgraded referee list, while upgraded over 7-day update status = success
     */
    public void syncUpgradedToSuccess(List<Integer> companyIdList) {
        // upgraded list
        Map<Integer, MoeReferralInfo> companyRefereeMap =
                this.getRefereeInfoMap(BusinessReferralConst.REFERRAL_UPGRADED, companyIdList);
        if (CollectionUtils.isEmpty(companyRefereeMap)) {
            return;
        }

        // subscription info
        CommonIdsParams commonIdsParams = new CommonIdsParams();
        commonIdsParams.setIds(new ArrayList<>(companyRefereeMap.keySet()));
        List<CompanyPermissionStateDto> permissionStateDtoList =
                paymentSubscriptionClient.getPermissionStateByCompanyIdList(commonIdsParams);

        // referralIdList
        List<Integer> referralIdList = companyRefereeMap.values().stream()
                .map(MoeReferralInfo::getReferralId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, MoeReferralInfo> referralInfoMap = this.getRefererInfoMap(referralIdList);
        Map<Integer, AtomicInteger> refererSuccessCountMap =
                this.getReferralStatusCountMap(referralIdList, BusinessReferralConst.REFERRAL_SUCCESS);

        // fixed rule and startArray
        List<MoeReferralFixedRewardRule> fixedRuleList =
                fixedRewardRuleMapper.getFixedRuleList(BusinessReferralConst.RULE_ACTIVE);
        int[] startArr = new int[fixedRuleList.size() + 1];
        for (int i = 0; i < fixedRuleList.size(); i++) {
            startArr[i] = fixedRuleList.get(i).getStartOffset();
        }
        startArr[fixedRuleList.size()] =
                fixedRuleList.get(fixedRuleList.size() - 1).getEndOffset() + 1;

        List<Future> futureList = new LinkedList<>();

        // parallel
        permissionStateDtoList.forEach(permissionStateDto -> {
            Future future =
                    ThreadPool.submit(() -> referralSyncService.updateUpgradeToSuccess(ReferralUpdateStatusDTO.builder()
                            .permissionStateDto(permissionStateDto)
                            .companyRefereeMap(companyRefereeMap)
                            .referralInfoMap(referralInfoMap)
                            .refererSuccessCountMap(refererSuccessCountMap)
                            .fixedRuleList(fixedRuleList)
                            .startArr(startArr)
                            .build()));
            futureList.add(future);
        });

        // 阻塞等待所有子线程运行完毕，以便 TimerMetrics 记录真实的运行时间
        futureList.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("syncUpgradedToSuccess future task error", e);
            }
        });
    }

    public void syncSuccessToOver3Month() {
        this.syncSuccessToOver3Month(Collections.emptyList());
    }

    public List<Integer> syncSuccessToOver3Month(List<Integer> companyIdList) {
        // success list
        Map<Integer, MoeReferralInfo> companyRefereeMap =
                this.getRefereeInfoMap(BusinessReferralConst.REFERRAL_SUCCESS, companyIdList);

        // filter charge over 3 months company
        Set<Integer> companyIdSet = companyRefereeMap.keySet();
        if (CollectionUtils.isEmpty(companyIdSet)) {
            return Collections.emptyList();
        }
        List<Integer> paid3TimesCompanyIdList = paymentStripeClient.getPaidGteCountByCompanyIdList(
                new ArrayList<>(companyIdSet), referralConfig.getInvoicePaidCount());
        if (CollectionUtils.isEmpty(paid3TimesCompanyIdList)) {
            return Collections.emptyList();
        }

        // referralIdList
        List<Integer> referralIdList = companyRefereeMap.values().stream()
                .map(MoeReferralInfo::getReferralId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, MoeReferralInfo> referralInfoMap = this.getRefererInfoMap(referralIdList);
        Map<Integer, AtomicInteger> refererOver3MonthsCountMap =
                this.getReferralStatusCountMap(referralIdList, BusinessReferralConst.REFERRAL_OVER_3_MONTH);

        // bonus rule
        List<MoeReferralBonusRewardRule> bonusRuleList =
                bonusRewardRuleMapper.getBonusRuleList(BusinessReferralConst.RULE_ACTIVE);
        Map<Integer, MoeReferralBonusRewardRule> targetBonusMap = bonusRuleList.stream()
                .collect(Collectors.toMap(MoeReferralBonusRewardRule::getTargetNumber, Function.identity()));

        List<Future> futureList = new LinkedList<>();
        // parallel
        paid3TimesCompanyIdList.forEach(companyId -> {
            Future future = ThreadPool.submit(
                    () -> referralSyncService.updateSuccessToOver3Month(ReferralUpdateStatusDTO.builder()
                            .companyRefereeMap(companyRefereeMap)
                            .referralInfoMap(referralInfoMap)
                            .refererOver3MonthsCountMap(refererOver3MonthsCountMap)
                            .targetBonusMap(targetBonusMap)
                            .companyId(companyId)
                            .build()));
            futureList.add(future);
        });

        // 阻塞等待所有子线程运行完毕，以便 TimerMetrics 记录真实的运行时间
        futureList.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("syncSuccessToOver3Month future task error", e);
            }
        });

        return paid3TimesCompanyIdList;
    }

    public boolean isValidReferralCode(MoeReferralInfo refereeInfo, ReferralInfoParams referralInfoParams) {
        Integer referralId = refereeInfo.getReferralId();
        // not binding referer
        if (Objects.isNull(referralId) || Objects.equals(referralId, 0)) {
            // exists referral code and not myself
            MoeReferralInfo refererInfo = referralInfoMapper.selectByReferralCode(referralInfoParams.getReferralCode());
            return (Objects.nonNull(refererInfo)
                    && !Objects.equals(refereeInfo.getReferralCode(), referralInfoParams.getReferralCode()));
        }
        MoeReferralInfo refererInfo = referralInfoMapper.selectByPrimaryKey(referralId);
        boolean isValid = Objects.equals(refererInfo.getReferralCode(), referralInfoParams.getReferralCode());
        if (isValid) {
            referralInfoParams.setReferralCode(refererInfo.getReferralCode());
        } else {
            referralInfoParams.setReferralCode(null);
        }
        return isValid;
    }

    public ReferralStatusDTO getReferralStatus(ReferralInfoParams referralInfoParams) {
        // purchase record
        Integer recordSize = paymentSubscriptionClient.countRecordListByCompanyId(referralInfoParams.getCompanyId());
        boolean isPurchased = Objects.nonNull(recordSize) && !Objects.equals(recordSize, 0);
        // valid
        MoeReferralInfo refereeInfo = referralInfoMapper.selectByCompanyIdAndAccountId(
                referralInfoParams.getCompanyId(), referralInfoParams.getAccountId());
        if (Objects.isNull(refereeInfo)) {
            refereeInfo = initializeReferralInfo(referralInfoParams);
        }
        boolean isValid = isValidReferralCode(refereeInfo, referralInfoParams);
        // subscriptionStatus
        CompanyPermissionStateDto permissionStateDto =
                paymentSubscriptionClient.getPermissionStateByCompanyId(referralInfoParams.getCompanyId());
        boolean isSubscribing =
                Objects.nonNull(permissionStateDto) && !Objects.equals(permissionStateDto.getLevel(), 0);
        // default 30 percent-off
        ReferralStatusDTO referralStatusDTO = ReferralStatusDTO.builder()
                .isPurchased(isPurchased)
                .isValid(isValid)
                .isSubscribing(isSubscribing)
                .referralCode(referralInfoParams.getReferralCode())
                .build();
        if (isValid) {
            referralStatusDTO.setAmountOff(referralConfig.getRewardAmountOff());
            referralStatusDTO.setPercentOff(referralConfig.getRewardPercentOff());
        }
        return referralStatusDTO;
    }

    public void syncReferralEndStatus(Integer companyId) {
        if (PrimitiveTypeUtil.isNullOrZeroOrNegative(companyId)) {
            return;
        }
        List<Integer> paidOver3MonthCompanyIdList = this.syncSuccessToOver3Month(new ArrayList<>(companyId));
        // first charge 3 months add popups reminder
        if (!CollectionUtils.isEmpty(paidOver3MonthCompanyIdList)) {
            reminderService.addFirstCharge3TimesEventReminder(companyId);
        } else {
            reminderService.addCommonDotEventReminder(companyId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelReferral(Integer companyId) {
        if (PrimitiveTypeUtil.isNullOrZeroOrNegative(companyId)) {
            return;
        }
        MoeCompany company = companyMapper.selectByPrimaryKey(companyId);
        referralInfoMapper.cancelByCompanyIdAndAccountId(companyId, company.getAccountId());
        reminderService.addCommonDotEventReminder(company);
    }

    @Transactional(rollbackFor = Exception.class)
    public void activeReferral(Integer companyId) {
        if (PrimitiveTypeUtil.isNullOrZeroOrNegative(companyId)) {
            return;
        }
        MoeCompany company = companyMapper.selectByPrimaryKey(companyId);
        referralInfoMapper.activeByCompanyIdAccountId(companyId, company.getAccountId());
        reminderService.addCommonDotEventReminder(company);
    }

    public void resetReferralBalance(Integer companyId, Integer accountId) {
        if (PrimitiveTypeUtil.isNullOrZeroOrNegative(companyId)) {
            return;
        }
        referralInfoMapper.resetBalanceByCompanyIdAccountId(companyId, accountId);
    }

    public void syncStripeBalance() {
        long lastDay = DateUtil.get10Timestamp() - 24 * 60 * 60;
        List<MoeReferralRewardRecord> rewardRecordList =
                referralRewardRecordMapper.getRewardListByStatus(BusinessReferralConst.BONUS_PENDING, lastDay);
        rewardRecordList.forEach(
                rewardRecord -> ThreadPool.execute(() -> referralSyncService.updateStripeBalance(rewardRecord)));
    }
}
