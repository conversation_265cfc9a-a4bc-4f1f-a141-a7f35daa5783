package com.moego.server.business.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.business.mapperbean.MoeStaffServiceArea;
import com.moego.server.business.service.dto.StaffDateServiceArea;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeStaffServiceAreaMapper extends DynamicDataSource<MoeStaffServiceAreaMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_service_area
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_service_area
     *
     * @mbg.generated
     */
    int insert(MoeStaffServiceArea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_service_area
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffServiceArea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_service_area
     *
     * @mbg.generated
     */
    MoeStaffServiceArea selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_service_area
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffServiceArea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_service_area
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffServiceArea record);

    MoeStaffServiceArea selectByStaffIdAndDateWithDeleted(
            @Param("staffId") Integer staffId, @Param("date") String date);

    List<StaffDateServiceArea> queryByStaffAndDateRangeWithDefault(
            @Param("staffId") Integer staffId, @Param("startDate") String startDate, @Param("endDate") String endDate);

    List<MoeStaffServiceArea> queryByBusinessNotDelete(
            @Param("businessId") Integer businessId,
            @Param("staffIdList") List<Integer> staffIdList,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<StaffDateServiceArea> queryByStaffAndDateRangeV2(
            @Param("staffIds") List<Integer> staffIds,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<StaffDateServiceArea> selectByStaffIdAndDateWithDefault(
            @Param("staffId") Integer staffId, @Param("date") String date);

    MoeStaffServiceArea selectByStaffIdAndDate(@Param("staffId") Integer staffId, @Param("date") String date);
}
