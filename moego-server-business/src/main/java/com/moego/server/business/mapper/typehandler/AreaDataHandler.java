package com.moego.server.business.mapper.typehandler;

import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.WorkingAreaDto;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

public class AreaDataHandler extends BaseTypeHandler<List<WorkingAreaDto>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<WorkingAreaDto> parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setString(i, JsonUtil.toJson(parameter));
    }

    @Override
    public List<WorkingAreaDto> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return rs.wasNull() ? null : JsonUtil.toList(rs.getString(columnName), WorkingAreaDto.class);
    }

    @Override
    public List<WorkingAreaDto> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return rs.wasNull() ? null : JsonUtil.toList(rs.getString(columnIndex), WorkingAreaDto.class);
    }

    @Override
    public List<WorkingAreaDto> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return cs.wasNull() ? null : JsonUtil.toList(cs.getString(columnIndex), WorkingAreaDto.class);
    }
}
