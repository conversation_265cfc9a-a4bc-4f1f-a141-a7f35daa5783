package com.moego.server.business.web;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.params.SortIdListParams;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.CommonIdParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.service.ReportV2Service;
import com.moego.server.business.service.StaffService;
import com.moego.server.business.service.StaffWorkingHourService;
import com.moego.server.business.service.dto.CalendarReminderDto;
import com.moego.server.business.service.dto.StaffInfoDetailDto;
import com.moego.server.business.service.dto.StaffShowCalendarDto;
import com.moego.server.business.utils.ReportUtil;
import com.moego.server.business.web.param.QueryReportParams;
import com.moego.server.business.web.vo.CertainArea;
import com.moego.server.business.web.vo.LocationQueryResponse;
import com.moego.server.business.web.vo.QueryAreasResponse;
import com.moego.server.business.web.vo.ServiceAreaInsideRequest;
import com.moego.server.business.web.vo.ServiceAreaInsideResult;
import com.moego.server.business.web.vo.StaffByWorkingDateVO;
import com.moego.server.business.web.vo.StaffCalendarVO;
import com.moego.server.business.web.vo.StaffServiceAreaResponse;
import com.moego.server.business.web.vo.StaffUpdateVo;
import com.moego.server.business.web.vo.UpdateAreaResponse;
import com.moego.server.business.web.vo.UpdateCertainAreaVO;
import com.moego.server.business.web.vo.UpdateStaffAreaItem;
import com.moego.server.business.web.vo.UpdateStaffAreaResponse;
import com.moego.server.business.web.vo.UpdateStaffServiceAreaVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/staff")
@Slf4j
public class StaffController {

    @Autowired
    private StaffService staffService;

    @Autowired
    private ReportV2Service reportV2Service;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private StaffWorkingHourService staffWorkingHourService;

    @PutMapping("/unlink")
    @Auth(AuthType.BUSINESS)
    public Boolean unlinkStaff(AuthContext context, @RequestBody InfoIdParams infoIdParams) {
        return staffService.unlinkStaff(context.companyId(), infoIdParams.getInfoId());
    }

    @DeleteMapping
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> deleteStaff(
            AuthContext context, @RequestParam Integer id, @RequestBody CommonIdParams commonIdParams) {
        if (commonIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "need id");
        }
        Integer tmpStaffId;
        if (id == null || id == 0) {
            tmpStaffId = commonIdParams.getId();
        } else {
            tmpStaffId = id;
        }
        return ResponseResult.success(
                staffService.deleteStaff(context.companyId(), context.getBusinessId(), tmpStaffId));
    }

    // 查询职员信息
    @PostMapping("/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<MoeStaff>> queryStaff(AuthContext context, @RequestBody MoeStaff moeStaff) {
        if (migrateHelper.isMigrate(context)) {
            moeStaff.setCompanyId(context.getCompanyId());
        } else {
            moeStaff.setBusinessId(context.getBusinessId());
        }
        return ResponseResult.success(staffService.queryMoeStaff(moeStaff));
    }

    @GetMapping("/listForCalendar")
    @Auth(AuthType.BUSINESS)
    public List<StaffCalendarVO> queryStaffForCalendar(
            AuthContext context,
            @RequestParam("startDate") String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        if (Objects.isNull(endDate)) {
            endDate = startDate;
        }
        if (Strings.isEmpty(startDate) || !DateUtil.checkDateFormat(startDate) || !DateUtil.checkDateFormat(endDate)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid date format");
        }
        List<MoeStaffDto> staffDTOList = staffService.getStaffListWithWorkingInfo(
                context.getBusinessId(), context.getStaffId(), startDate, endDate);

        var staffIds =
                staffDTOList.stream().map(MoeStaffDto::getId).map(Long::valueOf).toList();
        Map<Long, Map<String, BigDecimal>> staffEstimatedRevenueByDate = staffService.getStaffEstimatedRevenueByDate(
                context.companyId(), context.businessId(), staffIds, startDate, endDate);

        var staffCalendarVOList = new ArrayList<StaffCalendarVO>(staffDTOList.size());

        staffDTOList.forEach(staffDTO -> {
            var staffCalendarVO = new StaffCalendarVO(staffDTO);
            staffCalendarVO.setDateToEstimatedRevenue(
                    staffEstimatedRevenueByDate.getOrDefault(staffDTO.getId().longValue(), Map.of()));
            staffCalendarVOList.add(staffCalendarVO);
        });

        return staffCalendarVOList;
    }

    @GetMapping("/staffByWorkingDate")
    @Auth(AuthType.BUSINESS)
    public List<StaffByWorkingDateVO> queryStaffByWorkingDate(
            AuthContext context,
            @RequestParam("startDate") String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        if (Objects.isNull(endDate)) {
            endDate = startDate;
        }
        if (Strings.isEmpty(startDate) || !DateUtil.checkDateFormat(startDate) || !DateUtil.checkDateFormat(endDate)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid date format");
        }

        List<MoeStaffDto> staffDtoList =
                staffService.getStaffListForCalendar(context.getBusinessId(), context.getStaffId());
        var staffIdList = staffDtoList.stream().map(MoeStaffDto::getId).toList();

        return staffWorkingHourService.getStaffListByWorkingDateAndStaffIdList(
                context.getBusinessId(), staffIdList, startDate, endDate);
    }

    /**
     * 根据areaIds 查询 一个或多个Geo Area数据（certain area certain day 功能）
     * 仅老版本 app 使用，只返回 Polygon 类型
     */
    @GetMapping("/certainArea")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<QueryAreasResponse> queryAreaById(
            AuthContext context, @RequestParam("areaIds") List<Integer> areaIds) {
        return ResponseResult.success(staffService.queryAreaById(context.getBusinessId(), areaIds));
    }

    /**
     * 根据areaIds 查询 一个或多个Geo Area数据（certain area certain day 功能）
     */
    @GetMapping("/v2/certainArea")
    @Auth(AuthType.COMPANY)
    public QueryAreasResponse queryAreaByIdV2(
            AuthContext context,
            @RequestParam("areaIds") List<Integer> areaIds,
            @RequestParam(name = "businessId", required = false) Long businessId) {
        businessId = staffService.checkAndGetBusinessId(context.businessId(), businessId, context.companyId());
        return staffService.queryAreaByIdV2(businessId.intValue(), areaIds);
    }

    /**
     * 根据areaIds 查询 一个或多个Geo Area数据（certain area certain day 功能）
     * 仅老版本 app 使用，只返回 Polygon 类型
     */
    @GetMapping("/certainArea/business")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<QueryAreasResponse> queryBusinessAreas(AuthContext context) {
        return ResponseResult.success(staffService.queryBusinessAreas(context.getBusinessId()));
    }

    /**
     * 根据areaIds 查询 一个或多个Geo Area数据（certain area certain day 功能）
     */
    @GetMapping("/v2/certainArea/business")
    @Auth(AuthType.BUSINESS)
    public QueryAreasResponse queryBusinessAreasV2(
            AuthContext context, @RequestParam(name = "businessId", required = false) Long businessId) {
        businessId = staffService.checkAndGetBusinessId(context.businessId(), businessId, context.companyId());
        return staffService.queryBusinessAreasV2(businessId.intValue());
    }

    /**
     * 创建新的area或者更新一个已经存在的area（certain area certain day 功能）
     * <p>
     * 如果areaId给定则更新，否则创建新的areaId，如果给定areaId但是不存在也会报错。
     * 每一个更新的item都会有对应的success结果（true 或 false） 和 error message（如果报错）
     * <p>
     * area 支持 Polygon 与 zipcode 两种形式
     * Polygon格式 example: [[1.1, 1.2],[2.1, 2.2],[3.1, 3.2]]
     * 多边形至少要有三个点否则invalid
     */
    @Deprecated
    @PutMapping("/certainArea")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<UpdateAreaResponse> updateArea(AuthContext context, @RequestBody List<CertainArea> request) {
        return ResponseResult.success(staffService.updateArea(context.companyId(), context.getBusinessId(), request));
    }

    @PutMapping("/v2/certainArea")
    @Auth(AuthType.COMPANY)
    public UpdateAreaResponse updateAreaV2(AuthContext context, @RequestBody UpdateCertainAreaVO request) {
        Long businessId =
                staffService.checkAndGetBusinessId(context.businessId(), request.getBusinessId(), context.companyId());
        return staffService.updateArea(context.companyId(), businessId.intValue(), request.getCertainAreas());
    }

    /**
     * 删除一个area，不支持batch, 数据库软删除
     */
    @DeleteMapping("/certainArea")
    @Auth(AuthType.COMPANY)
    public ResponseResult<Integer> deleteArea(
            AuthContext context,
            @RequestParam Integer areaId,
            @RequestParam(name = "businessId", required = false) Long businessId) {
        businessId = staffService.checkAndGetBusinessId(context.businessId(), businessId, context.companyId());
        return ResponseResult.success(staffService.deleteArea(businessId.intValue(), areaId));
    }

    /**
     * 根据经纬度或 zipcode 查询所属 service area
     */
    @PostMapping("/certainArea/locationQuery")
    @Auth(AuthType.ACCOUNT)
    public LocationQueryResponse queryArea(
            AuthContext context, @RequestParam Integer businessId, @RequestBody GetAreasByLocationParams params) {
        return staffService.queryArea(businessId, params);
    }

    /**
     * 获取一个staff一天或者多天的service area（certain area certain day 功能）
     * <p>
     * 时间范围根据startDate 和 endDate确定，both startDate&endDate are inclusive
     * e.g. startDate=2020-12-12, endDate=2020-12-15, 会返回2020-12-12,
     * 2020-12-13, 2020-12-14， 2020-12-15 的数据
     * <p>
     * area数据包括polygon 和 areaId。如果staff没有设置area（也没有default area），则polygon为空数组
     * <p>
     * Note: 现在默认一个staff一天只有一个area（不会多个），返回也如此
     */
    @GetMapping("/serviceArea")
    @Auth(AuthType.COMPANY)
    public ResponseResult<StaffServiceAreaResponse> queryStaffServiceArea(
            AuthContext context,
            @RequestParam Integer staffId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(name = "businessId", required = false) Long businessId) {
        businessId = staffService.checkAndGetBusinessId(context.businessId(), businessId, context.companyId());
        return ResponseResult.success(
                staffService.queryStaffServiceArea(businessId.intValue(), staffId, startDate, endDate));
    }

    /**
     * （批量）更新staff某天的service area （certain area certain day 功能）
     * <p>
     * 需要提供staffId + areaId + (date 或者 isDefault为true）指定的area需要以及存在否则会失败。
     * <p>
     * 每一个更新的item都会有对应的success结果（true 或 false） 和 error message（如果报错）
     */
    @Deprecated
    @PutMapping("/serviceArea")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<UpdateStaffAreaResponse> updateStaffServiceArea(
            AuthContext context, @RequestBody List<UpdateStaffAreaItem> updates) {
        return ResponseResult.success(
                staffService.updateStaffServiceArea(context.getCompanyId(), context.getBusinessId(), updates));
    }

    /**
     * （批量）更新staff某天的service area （certain area certain day 功能）
     * <p>
     * 需要提供staffId + areaId + (date 或者 isDefault为true）指定的area需要以及存在否则会失败。
     * <p>
     * 每一个更新的item都会有对应的success结果（true 或 false） 和 error message（如果报错）
     */
    @PutMapping("/v2/serviceArea")
    @Auth(AuthType.COMPANY)
    public UpdateStaffAreaResponse updateStaffServiceAreaV2(
            AuthContext context, @RequestBody UpdateStaffServiceAreaVO updates) {
        Long businessId =
                staffService.checkAndGetBusinessId(context.businessId(), updates.getBusinessId(), context.companyId());
        return staffService.updateStaffServiceArea(
                context.getCompanyId(), businessId.intValue(), updates.getStaffAreas());
    }

    /**
     * 检查一个坐标，某一天，是否在staff 的service area内（certain area certain day 功能）
     * <p>
     * 如果该staff没有设置 CACD area， 也返回true（默认所有区域都可以）
     */
    @PostMapping("/serviceArea/isInside")
    @Auth(AuthType.COMPANY)
    public ResponseResult<ServiceAreaInsideResult> isLocationInsideArea(
            AuthContext context, @RequestBody ServiceAreaInsideRequest request) {
        Long businessId =
                staffService.checkAndGetBusinessId(context.businessId(), request.getBusinessId(), context.companyId());
        return ResponseResult.success(staffService.isLocationInsideArea(businessId.intValue(), request));
    }

    @GetMapping("/detail")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<StaffInfoDetailDto> getStaffDetail(AuthContext context, @RequestParam Integer staffId) {
        return ResponseResult.success(staffService.getStaffDetail(context.companyId(), staffId));
    }

    // 查询职员信息并附加该职员查看其他人的预约信息
    // todo 给日历用，查看那些人的预约信息
    @PostMapping("/listWithAppt")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<StaffShowCalendarDto>> queryStaffWithAppt(
            AuthContext context, @RequestBody MoeStaff moeStaff) {
        moeStaff.setBusinessId(context.getBusinessId());

        return ResponseResult.success(staffService.queryMoeStaffWithAppt(moeStaff));
    }

    @PostMapping("/sort")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> sortIdListParams(
            AuthContext context, @RequestBody SortIdListParams sortIdListParams) {
        return ResponseResult.success(staffService.sortMoeStaff(context.getCompanyId(), sortIdListParams.getIdList()));
    }

    // 更新职员信息
    @PutMapping("/update")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> updateSelect(AuthContext context, @Valid @RequestBody StaffUpdateVo staffUpdateVo) {
        staffUpdateVo.setBusinessId(context.getBusinessId());
        staffUpdateVo.setCompanyId(context.companyId());
        return ResponseResult.success(staffService.updateSelect(staffUpdateVo));
    }

    @GetMapping("/push/calendar")
    @Auth(AuthType.BUSINESS)
    public CalendarReminderDto calendarReminderQuery(AuthContext context) {
        return staffService.calendarReminderQuery(context.getStaffId());
    }

    @PutMapping("/push/calendar")
    @Auth(AuthType.BUSINESS)
    public Boolean calendarReminderUpdate(AuthContext context, @Valid @RequestBody CalendarReminderDto reminderDto) {
        staffService.calendarReminderUpdate(context.getStaffId(), reminderDto);
        return true;
    }

    //    //更新职员对应的查看其他职员对应关系
    //    @PostMapping("/updateStaffAppt")
    //    public ResponseResult updateStaffAppt(@RequestBody MoeStaff moeStaff) {
    //        return ResponseResult.success(staffService.updateStaffAppt(moeStaff));
    //    }

    /**
     * 根据businessId 和 雇佣日期 获取staff列表 ,包含已删除的staff
     *
     * @param params startDate, endDate, businessIds
     */
    @GetMapping("/hire/list")
    @Operation(
            summary = "如果查询单天(2021-07-01)，startDate是2021-07-01  endDate是2021-07-01\n"
                    + "如果查询多天，startDate是2021-07-01  endDate是2021-07-30\n"
                    + "startDate和endDate不会为空，一定会有意义\n")
    @Auth(AuthType.BUSINESS)
    public List<MoeStaff> getStaffsByBusinessIdAndHireDate(@Valid QueryReportParams params) {
        // date 检查, 查询范围不能超过1年
        ReportUtil.checkQueryRange(params.getStartDate(), params.getEndDate());
        params.setBusinessId(AuthContext.get().getBusinessId())
                .setTokenCompanyId(AuthContext.get().companyId());
        return reportV2Service.getStaffsByBusinessIdAndHireDate(params);
    }
}
