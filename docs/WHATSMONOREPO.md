# 什么是 Monorepo？

Monorepo（单体仓库）是一种将多个相关项目统一管理在单个代码仓库中的开发模式。

## 📖 目录

- [核心概念](#核心概念)
- [MoeGo 的选择原因](#moego-的选择原因)
- [实际收益](#实际收益)
- [最佳实践](#最佳实践)

## 🎯 核心概念

### 代码管理演进路径

```mermaid
graph LR
    A[单体应用<br/>Monolith] --> B[多仓库<br/>MultiRepo] --> C[单仓库多模块<br/>MonoRepo]
    A --> D[❌ 代码庞大<br/>构建缓慢]
    B --> E[❌ 依赖复杂<br/>协作困难]
    C --> F[✅ 统一管理<br/>高效协作]
```

### 简单对比

| 场景 | MultiRepo | MonoRepo |
|-----|-----------|----------|
| **跨模块变更** | 需要多个 PR | 单次提交完成 |
| **依赖管理** | 版本冲突频繁 | 统一版本管理 |
| **CI/CD** | 多套流程维护 | 统一构建部署 |
| **代码复用** | 重复代码多 | 便于共享重构 |

## 🤔 MoeGo 的选择原因

### 我们遇到的实际痛点

**🔥 多仓库协作噩梦**
- 一个功能涉及 5-6 个仓库
- 必须按依赖顺序提交，经常出错
- 代码审查分散，容易遗漏

**📦 依赖地狱**
- go.mod 版本冲突是家常便饭
- 手动同步版本耗时且易错
- 无法原子性更新依赖

**🏗️ CI/CD 维护成本**
- 每个仓库单独配置构建
- 集成测试复杂且脆弱
- 部署顺序错误导致服务不可用

### 具体案例

**场景：新增一个 API 接口**

*MultiRepo 时代：*
```bash
# 需要操作 4 个仓库，按顺序进行
1. moego-proto → 定义 API
2. moego-common → 更新公共库  
3. moego-customer → 实现业务逻辑
4. moego-frontend → 对接前端

# 每个仓库都要：
- 单独提 PR
- 等待审查
- 部署测试
- 处理版本冲突
```

*MonoRepo 时代：*
```bash
# 一次提交完成所有变更
1. 编辑 backend/proto/customer/v1/customer.proto
2. 实现 backend/app/customer/service/customer_service.go
3. 更新 frontend/app/console/src/pages/customer.tsx
4. 一次性提交，自动化测试和部署
```

## 📊 实际收益

### 量化指标

| 指标 | MultiRepo | MonoRepo | 提升 |
|-----|-----------|----------|------|
| **功能开发周期** | 3-5 天 | 1-2 天 | **60%↑** |
| **构建时间** | 15-20 分钟 | 5-8 分钟 | **3x** |
| **部署频率** | 1-2 次/天 | 5-8 次/天 | **4x** |
| **代码复用率** | 30% | 70% | **40%↑** |

### 开发体验提升

**代码审查更高效**
```bash
# 以前：跨仓库 PR 难以 review
PR #123 → moego-proto
PR #124 → moego-common  
PR #125 → moego-customer
PR #126 → moego-frontend

# 现在：一个 PR 看全貌
PR #123 → moego (包含完整功能变更)
```

**调试和追踪更简单**
```bash
# 以前：跨仓库追踪调用链
git log --oneline moego-customer  # 找不到完整逻辑
git log --oneline moego-common    # 版本对不上

# 现在：单仓库全局视角
git log --oneline backend/app/customer  # 完整的变更历史
```

## 💡 最佳实践

### 1. 合理的项目结构

```
moego/
├── backend/app/               # 微服务按业务域划分
│   ├── customer/             # 客户管理域
│   ├── sales/                # 销售管理域
│   └── fulfillment/          # 订单履约域
├── backend/common/           # 按技术层次划分
│   ├── rpc/                  # RPC 框架
│   ├── database/             # 数据库抽象
│   └── utils/                # 工具库
└── frontend/app/             # 前端按应用划分
    └── console/              # 管理控制台
```

### 2. 智能化工具链

**自动依赖管理**
```bash
# 修改 go.mod 后自动同步
make gazelle  # 自动更新所有 BUILD.bazel 文件
```

**增量构建和测试**
```bash
# 只构建变更部分
make diff     # 检测变更的服务
make build dir=//backend/app/customer  # 只编译变更的服务
make test dir=//backend/app/customer   # 只测试相关模块
```

**统一代码质量**
```bash
# 一套配置管理所有代码
make lint     # 统一的代码检查标准
```

### 3. 权限精细化管理

使用 CODEOWNERS 精确控制：
```bash
# 团队级权限
/backend/app/customer/    @crm-team
/backend/app/sales/       @sales-team

# 关键组件多人审核
/backend/common/rpc/      @platform-team @tech-lead
```

### 4. 性能优化策略

**Bazel 缓存优化**
```bash
# 本地缓存
build --disk_cache=~/.cache/bazel

# 团队共享缓存（如果有条件）
build --remote_cache=https://cache.company.com
```

**并行构建优化**
```bash
# 充分利用 CPU 资源
build --jobs=$(nproc)  # 使用所有 CPU 核心
```

## 🚨 适用场景

### ✅ 适合 Monorepo 的项目

- **业务相关性强**：模块间有业务依赖
- **技术栈统一**：主要使用相同技术
- **团队协作频繁**：需要跨模块协作
- **版本同步需求**：需要原子性部署

### ❌ 不适合的场景

- **完全独立产品**：无业务关联的项目
- **技术栈差异大**：Java + Python + Go 混合
- **团队地域分散**：跨时区协作较少
- **权限要求严格**：需要完全隔离的项目

## 🎉 总结

对于像 MoeGo 这样的微服务项目，Monorepo 带来的核心价值：

1. **简化工作流** - 一次提交，端到端变更
2. **提升质量** - 统一标准，减少集成问题  
3. **加速迭代** - 增量构建，快速反馈
4. **降低成本** - 减少重复配置和维护

Monorepo 不是万能解决方案，但在合适的场景下能显著提升团队效率。

---

## 📚 延伸阅读

- [Bazel 使用指南](./USE_BAZEL.md) - 了解我们的构建工具选择
- [项目主 README](../README.md) - 快速开始使用 MoeGo