package com.moego.common.enums.payment;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/7/16
 */
@Getter
public enum SplitSyncRecordSyncModeEnum {
    MODE_SYNC(0, "同步执行, 指的是只关注受理结果, 不关注处理结果"),
    MODE_ASYNC(5, "异步执行, 即关注受理结果，也关注处理结果"),
    ;

    private final Integer code;
    private final String desc;

    SplitSyncRecordSyncModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
