package com.moego.common.enums;

public interface ReportConst {
    // 按日期查询sales report的方式
    Integer SALES_REPORT_BY_WEEK = 1;
    Integer SALES_REPORT_BY_MONTH = 2;
    Integer SALES_REPORT_BY_DAY_OF_WEEK = 3;

    // 按位置信息查询sales report的方式
    Integer SALES_REPORT_BY_CITY = 1;
    Integer SALES_REPORT_BY_ZIPCODE = 2;
    Integer SALES_REPORT_BY_SERVICEAREA = 3;

    // 查询payroll report的类型
    byte PAYROLL_TYPE_SERVICE = 1;
    byte PAYROLL_TYPE_HOURLY = 2;
    byte PAYROLL_TYPE_TIPS = 3;

    // report默认值
    String DEFAULT_VALUE = "N/A";

    String PAY_RATE_FORMAT = "%s%%";
    String FIXED_RATE_FORMAT = "By fixed rate - %s%%";
    String TIER_RATE_FORMAT = "By tiered rate";
}
