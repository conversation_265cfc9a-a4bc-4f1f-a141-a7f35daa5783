package com.moego.lib.aws.model;

import com.moego.lib.utils.CoreUtils;
import com.moego.lib.utils.DateTimeUtils;
import com.moego.lib.utils.PathUtils;
import com.moego.lib.utils.StringUtils;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.MessageDigest;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import lombok.SneakyThrows;
import software.amazon.awssdk.core.internal.util.Mimetype;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.http.Header;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.ObjectCannedACL;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.Tag;
import software.amazon.awssdk.services.s3.model.Tagging;

public record S3Context(
        byte[] data,
        Long contentLength,
        String contentType,
        String contentMD5,
        String contentEncoding,
        String contentLanguage,
        String contentDisposition,
        String cannedACL,
        Instant expiredTime,
        Map<String, String> metadata,
        Map<String, String> tags) {

    public static final String S3_METADATA_HEADER_PREFIX = "x-amz-meta-";
    public static final String S3_SYS_HEADER_ACL = "x-amz-acl";
    public static final String S3_SYS_HEADER_CONTENT_TYPE = Header.CONTENT_TYPE;
    public static final String S3_SYS_HEADER_CONTENT_MD5 = Header.CONTENT_MD5;
    public static final String S3_SYS_HEADER_CONTENT_LENGTH = Header.CONTENT_LENGTH;
    public static final String S3_SYS_HEADER_CONTENT_ENCODING = "Content-Encoding";
    public static final String S3_SYS_HEADER_CONTENT_LANGUAGE = "Content-Language";
    public static final String S3_SYS_HEADER_CONTENT_DISPOSITION = "Content-Disposition";

    public static final Set<String> S3_SYSTEM_HEADERS = Set.of(
            S3_SYS_HEADER_ACL,
            S3_SYS_HEADER_CONTENT_TYPE,
            S3_SYS_HEADER_CONTENT_MD5,
            S3_SYS_HEADER_CONTENT_LENGTH,
            S3_SYS_HEADER_CONTENT_ENCODING,
            S3_SYS_HEADER_CONTENT_LANGUAGE,
            S3_SYS_HEADER_CONTENT_DISPOSITION);

    public S3Context {
        contentMD5 = processMD5(contentMD5);
    }

    public boolean hasMetadata() {
        return this.metadata() != null && !this.metadata().isEmpty();
    }

    public boolean hasTag() {
        return this.tags() != null && !this.tags().isEmpty();
    }

    public static boolean isSystemHeader(String key) {
        return !StringUtils.isBlank(key) && S3_SYSTEM_HEADERS.stream().anyMatch(k -> k.equalsIgnoreCase(key));
    }

    public static PutObjectRequest toPutObjectRequest(String bucketName, String key, S3Context ctx) {
        var builder = PutObjectRequest.builder();
        builder.bucket(bucketName);
        builder.key(key);
        if (ctx != null) {
            if (!StringUtils.isBlank(ctx.contentType())) {
                builder.contentType(ctx.contentType());
            }
            if (ctx.contentLength() != null) {
                builder.contentLength(ctx.contentLength());
            }
            if (!StringUtils.isBlank(ctx.contentMD5())) {
                builder.contentMD5(ctx.contentMD5());
            }
            if (!StringUtils.isBlank(ctx.contentEncoding())) {
                builder.contentEncoding(ctx.contentEncoding());
            }
            if (!StringUtils.isBlank(ctx.contentLanguage())) {
                builder.contentLanguage(ctx.contentLanguage());
            }
            if (!StringUtils.isBlank(ctx.contentDisposition())) {
                builder.contentDisposition(ctx.contentDisposition());
            }
            if (ctx.expiredTime() != null) {
                builder.expires(ctx.expiredTime());
            }
            if (ctx.hasMetadata()) {
                builder.metadata(ctx.metadata());
            }
            if (!StringUtils.isBlank(ctx.cannedACL())) {
                builder.acl(ObjectCannedACL.fromValue(ctx.cannedACL()));
            }
            if (ctx.hasTag()) {
                List<Tag> taglist = new ArrayList<>();
                ctx.tags()
                        .forEach((k, v) ->
                                taglist.add(Tag.builder().key(k).value(v).build()));
                builder.tagging(Tagging.builder().tagSet(taglist).build());
            }
        }
        return builder.build();
    }

    public static RequestBody toRequestBody(S3Context ctx) {
        if (ctx.data() != null && 0 < ctx.data().length && 0 < ctx.contentLength()) {
            return RequestBody.fromContentProvider(
                    () -> new ByteArrayInputStream(ctx.data()), ctx.contentLength(), ctx.contentType());
        }

        return RequestBody.empty();
    }

    public static Map<String, String> toUploadHeaders(S3Context ctx) {
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        if (!StringUtils.isBlank(ctx.contentType())) {
            headers.put(S3_SYS_HEADER_CONTENT_TYPE, ctx.contentType());
        }
        if (!StringUtils.isBlank(ctx.contentMD5())) {
            headers.put(S3_SYS_HEADER_CONTENT_MD5, ctx.contentMD5());
        }
        if (!StringUtils.isBlank(ctx.contentEncoding())) {
            headers.put(S3_SYS_HEADER_CONTENT_ENCODING, ctx.contentEncoding());
        }
        if (!StringUtils.isBlank(ctx.contentLanguage())) {
            headers.put(S3_SYS_HEADER_CONTENT_LANGUAGE, ctx.contentLanguage());
        }
        if (!StringUtils.isBlank(ctx.contentDisposition())) {
            headers.put(S3_SYS_HEADER_CONTENT_DISPOSITION, ctx.contentDisposition());
        }
        if (!StringUtils.isBlank(ctx.cannedACL())) {
            headers.put(S3_SYS_HEADER_ACL, ctx.cannedACL());
        }
        if (ctx.hasMetadata()) {
            ctx.metadata().forEach((k, v) -> {
                if (!S3Context.isSystemHeader(k)) {
                    headers.put(S3Context.S3_METADATA_HEADER_PREFIX + k, v);
                }
            });
        }

        return headers;
    }

    @SneakyThrows
    public static S3Context from(
            String text,
            String contentEncoding,
            String contentLanguage,
            String contentDisposition,
            String cannedACL,
            Duration retentionDuration,
            Map<String, String> metadata,
            Map<String, String> tags) {
        return from(
                text.getBytes(StandardCharsets.UTF_8),
                Mimetype.MIMETYPE_TEXT_PLAIN,
                contentEncoding,
                contentLanguage,
                contentDisposition,
                cannedACL,
                retentionDuration,
                metadata,
                tags);
    }

    @SneakyThrows
    public static S3Context from(
            File file,
            String contentEncoding,
            String contentLanguage,
            String contentDisposition,
            String cannedACL,
            Duration retentionDuration,
            Map<String, String> metadata,
            Map<String, String> tags) {
        return from(
                file.toPath(),
                contentEncoding,
                contentLanguage,
                contentDisposition,
                cannedACL,
                retentionDuration,
                metadata,
                tags);
    }

    @SneakyThrows
    public static S3Context from(
            Path path,
            String contentEncoding,
            String contentLanguage,
            String contentDisposition,
            String cannedACL,
            Duration retentionDuration,
            Map<String, String> metadata,
            Map<String, String> tags) {
        var filename = path.getFileName();
        var ext = PathUtils.extensions(path);
        var type = Mimetype.getInstance().getMimetype(path);
        Map<String, String> map = new HashMap<>();
        if (filename != null) {
            map.put("file/name", filename.toString());
        }
        if (!StringUtils.isBlank(type)) {
            map.put("file/type", type);
        }
        if (!StringUtils.isBlank(ext)) {
            map.put("file/extension", ext);
        }
        if (metadata != null && !metadata.isEmpty()) {
            map.putAll(metadata);
        }

        return from(
                Files.readAllBytes(path),
                type,
                contentEncoding,
                contentLanguage,
                contentDisposition,
                cannedACL,
                retentionDuration,
                map,
                tags);
    }

    public static S3Context from(
            InputStream stream,
            String contentEncoding,
            String contentLanguage,
            String contentDisposition,
            String cannedACL,
            Duration retentionDuration,
            Map<String, String> metadata,
            Map<String, String> tags) {
        return from(
                stream,
                Mimetype.MIMETYPE_OCTET_STREAM,
                contentEncoding,
                contentLanguage,
                contentDisposition,
                cannedACL,
                retentionDuration,
                metadata,
                tags);
    }

    @SneakyThrows
    public static S3Context from(
            InputStream stream,
            String contentType,
            String contentEncoding,
            String contentLanguage,
            String contentDisposition,
            String cannedACL,
            Duration retentionDuration,
            Map<String, String> metadata,
            Map<String, String> tags) {
        return from(
                stream.readAllBytes(),
                contentType,
                contentEncoding,
                contentLanguage,
                contentDisposition,
                cannedACL,
                retentionDuration,
                metadata,
                tags);
    }

    public static S3Context from(
            byte[] data,
            String contentEncoding,
            String contentLanguage,
            String contentDisposition,
            String cannedACL,
            Duration retentionDuration,
            Map<String, String> metadata,
            Map<String, String> tags) {
        return from(
                data,
                Mimetype.MIMETYPE_OCTET_STREAM,
                contentEncoding,
                contentLanguage,
                contentDisposition,
                cannedACL,
                retentionDuration,
                metadata,
                tags);
    }

    public static S3Context from(
            byte[] data,
            String contentType,
            String contentEncoding,
            String contentLanguage,
            String contentDisposition,
            String cannedACL,
            Duration duration,
            Map<String, String> metadata,
            Map<String, String> tags) {

        return new S3Context(
                data,
                (long) data.length,
                contentType,
                md5(data),
                contentEncoding,
                contentLanguage,
                contentDisposition,
                cannedACL,
                duration == null ? null : Instant.ofEpochMilli(DateTimeUtils.now() + duration.toMillis()),
                metadata,
                tags);
    }

    public static S3Context from(byte[] bytes, GetObjectResponse response) {
        if (response == null) {
            return new S3Context(bytes, null, null, null, null, null, "", null, null, null, null);
        }
        return new S3Context(
                bytes,
                response.contentLength(),
                response.contentType(),
                processETag(response.eTag()),
                response.contentEncoding(),
                response.contentLanguage(),
                response.contentDisposition(),
                null,
                response.expires(),
                response.metadata(),
                null);
    }

    public static String processETag(String eTag) {
        if (!StringUtils.isBlank(eTag)
                && 1 < eTag.length()
                && '"' == eTag.charAt(0)
                && '"' == eTag.charAt(eTag.length() - 1)) {
            return eTag.substring(1, eTag.length() - 1);
        } else {
            return eTag;
        }
    }

    public static String processMD5(String md5) {
        if (!StringUtils.isBlank(md5) && 32 == md5.length() && CoreUtils.isDigestString(md5)) {
            return Base64.getEncoder().encodeToString(CoreUtils.hexStringToBytes(md5));
        }

        return md5;
    }

    @SneakyThrows
    public static String md5(byte[] bytes) {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(bytes);
        return Base64.getEncoder().encodeToString(md.digest());
    }

    public static String s3MD5ToDigest(String digest) {
        return CoreUtils.bytesToHexString(Base64.getDecoder().decode(digest));
    }

    public static String generateS3URI(String bucketName, String key) {
        return "s3://" + bucketName + "/" + key;
    }
}
