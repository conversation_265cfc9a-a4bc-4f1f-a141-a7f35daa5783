package com.moego.lib.common.http.util;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class HttpUtilTest {

    @Test
    void testGetService() {
        assertThat(HttpUtil.getService("localhost:8080")).isEqualTo("localhost");
        assertThat(HttpUtil.getService("localhost")).isEqualTo("localhost");
        assertThat(HttpUtil.getService("service.ns")).isEqualTo("service");
        assertThat(HttpUtil.getService("service.ns.xx")).isEqualTo("service");
        assertThat(HttpUtil.getService("http://service.ns.xx:8080")).isEqualTo("service");
        assertThat(HttpUtil.getService("http://service.ns.xx")).isEqualTo("service");
    }

    @Test
    void testRemoveSchema() {
        assertThat(HttpUtil.removeSchema("http://service.ns.xx")).isEqualTo("service.ns.xx");
        assertThat(HttpUtil.removeSchema("http://service:8080")).isEqualTo("service:8080");
        assertThat(HttpUtil.removeSchema("service:8080")).isEqualTo("service:8080");
    }

    @Test
    void getHostIp() {
        assertThat(HttpUtil.getHostIp("http://service.ns.xx")).isEqualTo("service.ns.xx:80");
        assertThat(HttpUtil.getHostIp("service:8080")).isEqualTo("service:8080");
    }

    @Test
    void getPort() {
        assertThat(HttpUtil.getPort("service:8080")).isEqualTo(8080);
        assertThat(HttpUtil.getPort("http://service")).isEqualTo(80);
        assertThat(HttpUtil.getPort("http://service:8080")).isEqualTo(8080);
    }

    @Test
    void getDomain() {
        assertThat(HttpUtil.getDomain("http://service.ns:8080")).isEqualTo("service.ns");
        assertThat(HttpUtil.getDomain("http://service:8080")).isEqualTo("service");
        assertThat(HttpUtil.getDomain("http://service")).isEqualTo("service");
    }
}
