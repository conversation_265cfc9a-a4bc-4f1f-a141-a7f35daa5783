syntax = "proto3";

package moego.api.auth;

option java_multiple_files = true;
option java_package = "com.moego.api.api";
option java_outer_classname = "AuthApiLayerProto";

// The greeting service definition.
service Greeter {
  // Sends a greeting
  rpc <PERSON><PERSON><PERSON> (HelloRequest) returns (HelloReply) {}
}

// The request message containing the user's name.
message HelloRequest {
  string name = 1;
}

// The response message containing the greetings
message HelloReply {
  string message = 1;
}