package testaccount

import (
	"context"
	"strconv"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	entity "github.com/MoeGolibrary/moego/backend/app/tools/repo/testaccount/entity"
	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

const maxAge = time.Minute * 15

type ReadWriter interface {
	Tx(ctx context.Context, fn func(ReadWriter) error) error

	GetForUpdate(ctx context.Context, attributes *entity.Attributes) (*entity.TestAccount, error)
	GetForUpdateByID(ctx context.Context, id int64) (*entity.TestAccount, error)
	GetForUpdateByEmail(ctx context.Context, email string) (*entity.TestAccount, error)

	Occupy(ctx context.Context, accountID int64, borrower string, shared bool) (*entity.LeaseContract, error)
	Return(ctx context.Context, contractID int64) error
	Save(ctx context.Context, account *entity.TestAccount) error

	ReleaseOverdueTestAccounts(ctx context.Context) error

	ListTestAccounts(ctx context.Context, pageSize int32, pageToken string) (
		[]*entity.TestAccount, *string, int64, error)
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	// 这个名字是自定义的，要和config.yaml中的数据库配置名字一致
	// 得到的 db 是一个原生的 *gorm.DB
	db, err := igorm.NewClientProxy("postgres")
	if err != nil {
		panic(err)
	}
	return &impl{
		db: db,
	}
}

func (i impl) Tx(ctx context.Context, fn func(ReadWriter) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return fn(&impl{db: tx})
	})
}

// GetForUpdate 获取一个未被占用的账号，只在公共账号（无 owner）中查找
func (i impl) GetForUpdate(ctx context.Context, attributes *entity.Attributes) (*entity.TestAccount, error) {
	account := &entity.TestAccount{}

	if err := i.db.WithContext(ctx).Model(&entity.TestAccount{}).
		Where("occupied = ?", false).
		Where("owner = ?", "").
		Scopes(attributes.Filter).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		First(account).Error; err != nil {
		return nil, err
	}
	return account, nil
}

func (i impl) GetForUpdateByID(ctx context.Context, id int64) (*entity.TestAccount, error) {
	account := &entity.TestAccount{}
	if err := i.db.WithContext(ctx).Model(&entity.TestAccount{}).
		Where("id = ?", id).
		Where("occupied = ?", false).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		First(account).Error; err != nil {
		return nil, err
	}
	return account, nil
}

func (i impl) GetForUpdateByEmail(ctx context.Context, email string) (*entity.TestAccount, error) {
	account := &entity.TestAccount{}
	if err := i.db.WithContext(ctx).Model(&entity.TestAccount{}).
		Where("email = ?", email).
		Where("occupied = ?", false).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		First(account).Error; err != nil {
		return nil, err
	}
	return account, nil
}

func (i impl) Return(ctx context.Context, contractID int64) error {
	// get contract
	var contract entity.LeaseContract
	if err := i.db.WithContext(ctx).Model(&entity.LeaseContract{}).
		Where("id = ?", contractID).
		First(&contract).Error; err != nil {
		return err
	}

	// check if contract has been returned
	if contract.ReturnTime != nil {
		return nil
	}

	// set contract return time
	if err := i.db.WithContext(ctx).Model(&entity.LeaseContract{}).
		Where("id = ?", contractID).
		Update("return_time", time.Now()).Error; err != nil {
		return err
	}

	// release account
	if !contract.Shared {
		return i.db.WithContext(ctx).Model(&entity.TestAccount{}).
			Where("id = ?", contract.TestAccountID).
			Update("occupied", false).Error
	}
	return nil
}

func (i impl) Save(ctx context.Context, account *entity.TestAccount) error {
	return i.db.WithContext(ctx).Model(&entity.TestAccount{}).Create(account).Error
}

func (i impl) Occupy(ctx context.Context, accountID int64, borrower string, shared bool) (
	*entity.LeaseContract, error) {

	if !shared {
		// set to be occupied
		if err := i.db.WithContext(ctx).Model(&entity.TestAccount{}).
			Where("id = ?", accountID).
			Update("occupied", true).Error; err != nil {
			return nil, err
		}
	}

	// create lease contract
	due := time.Now().Add(maxAge)
	contract := entity.LeaseContract{
		TestAccountID: accountID,
		Borrower:      borrower,
		BorrowTime:    time.Now(),
		DueTime:       &due,
		Shared:        shared,
	}

	if err := i.db.WithContext(ctx).Model(&entity.LeaseContract{}).
		Create(&contract).Error; err != nil {
		return nil, err
	}

	return &contract, nil
}

func (i impl) ReleaseOverdueTestAccounts(ctx context.Context) error {
	// 1. 先查出有占用记录的账号
	sub := i.db.Model(&entity.LeaseContract{}).Select("distinct test_account_id").
		// 未归还
		Where("return_time is null").
		// 没有设置到期时间（长期占用）或者 到期时间是将来的某个时间
		Where("due_time is null OR due_time >= ?", time.Now())

	// 2. 有占用标记但是不在占用列表里的账号，需要释放
	return i.db.WithContext(ctx).Model(&entity.TestAccount{}).
		Where("occupied = ?", true).
		Where("id not in (?)", sub).
		Update("occupied", false).Error
}

func (i impl) ListTestAccounts(ctx context.Context, pageSize int32, pageToken string) (
	[]*entity.TestAccount, *string, int64, error) {

	var accounts []*entity.TestAccount

	if pageSize == 0 {
		pageSize = 20
	}

	var pageNum int64

	// 将 pageToken 作为页码使用
	if pageToken != "" {
		var err error
		pageNum, err = strconv.ParseInt(pageToken, 10, 64)
		if err != nil {
			return nil, nil, 0, err
		}
	} else {
		pageNum = 1
	}

	offset := int(pageNum-1) * int(pageSize)
	if err := i.db.WithContext(ctx).
		Order(clause.OrderByColumn{Column: clause.Column{Name: "id"}, Desc: false}).
		Limit(int(pageSize)).
		Offset(offset).
		Find(&accounts).Error; err != nil {
		return nil, nil, 0, err
	}

	// count total test accounts
	var total int64
	if err := i.db.WithContext(ctx).Model(&entity.TestAccount{}).Count(&total).Error; err != nil {
		return nil, nil, 0, err
	}

	// 计算下一个页面的页码
	if len(accounts) < int(pageSize) {
		return accounts, nil, total, nil
	}
	nextPageToken := strconv.FormatInt(pageNum+1, 10)
	return accounts, &nextPageToken, total, nil
}
