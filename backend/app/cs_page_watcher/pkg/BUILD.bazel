load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "pkg",
    srcs = [
        "lock.go",
        "string_utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/pkg",
    visibility = ["//visibility:public"],
)

go_test(
    name = "pkg_test",
    srcs = ["lock_test.go"],
    embed = [":pkg"],
    deps = ["@com_github_stretchr_testify//assert"],
)
