CREATE TABLE moego_pay_custom_fee_approval
(
    id                      TEXT PRIMARY KEY,
    metadata                JSONB  NOT NULL          default '{}',
    company_id              BIGINT NOT NULL,
    account_id              BIGINT NOT NULL,
    owner_email             TEXT   NOT NULL,
    terminal_percentage     NUMERIC(5, 2),
    terminal_fixed          NUMERIC(12, 2),
    non_terminal_percentage NUMERIC(5, 2),
    non_terminal_fixed      NUMERIC(12, 2),
    min_volume              NUMERIC(12, 2),
    creator                 TEXT   NOT NULL,
    handler                 TEXT,
    approval_state          TEXT   NOT NULL,
    created_at              TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at              TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    handled_at              TIMESTAMP WITH TIME ZONE
);

COMMENT ON TABLE moego_pay_custom_fee_approval IS 'Moego Pay Custom Fee Approval, 此表只做审批记录，最终生效的费率以业务系统为准';
COMMENT ON COLUMN moego_pay_custom_fee_approval.id IS 'Approval ID';
COMMENT ON COLUMN moego_pay_custom_fee_approval.metadata IS 'Metadata';
COMMENT ON COLUMN moego_pay_custom_fee_approval.company_id IS 'Company ID';
COMMENT ON COLUMN moego_pay_custom_fee_approval.account_id IS 'Account ID';
COMMENT ON COLUMN moego_pay_custom_fee_approval.owner_email IS 'Email of the owner';
COMMENT ON COLUMN moego_pay_custom_fee_approval.terminal_percentage IS 'Percentage for terminal transactions';
COMMENT ON COLUMN moego_pay_custom_fee_approval.terminal_fixed IS 'Fixed amount in dollars for terminal transactions';
COMMENT ON COLUMN moego_pay_custom_fee_approval.non_terminal_percentage IS 'Percentage for non-terminal transactions';
COMMENT ON COLUMN moego_pay_custom_fee_approval.non_terminal_fixed IS 'Fixed amount in dollars for non-terminal transactions';
COMMENT ON COLUMN moego_pay_custom_fee_approval.min_volume IS 'Minimum transaction volume required for Moego Pay';
COMMENT ON COLUMN moego_pay_custom_fee_approval.creator IS 'Creator of the approval';
COMMENT ON COLUMN moego_pay_custom_fee_approval.handler IS 'handler of the approval';
COMMENT ON COLUMN moego_pay_custom_fee_approval.approval_state IS 'State of the approval (PENDING, APPROVED, REJECTED)';
COMMENT ON COLUMN moego_pay_custom_fee_approval.created_at IS 'Timestamp of when the record was created';
COMMENT ON COLUMN moego_pay_custom_fee_approval.updated_at IS 'Timestamp of when the record was last updated';
COMMENT ON COLUMN moego_pay_custom_fee_approval.handled_at IS 'Timestamp of when the approval was processed';