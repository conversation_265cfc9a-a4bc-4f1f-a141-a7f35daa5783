#套餐权限控制：和原 payroll 权限一起控制，payroll2控制新增功能的权限
INSERT INTO `moe_payment`.`moe_feature` (`name`, `code`, `allow_type`, `enable`, `quota`)
VALUES ('payroll 2', 'payroll2', 2, 0, -1);

INSERT INTO `moe_payment`.`moe_plan_feature_relation` (`level`, `code`, `allow_type`, `enable`, `quota`)
VALUES (1001, 'payroll2', 2, 0, -1);
INSERT INTO `moe_payment`.`moe_plan_feature_relation` (`level`, `code`, `allow_type`, `enable`, `quota`)
VALUES (1101, 'payroll2', 2, 1, -1);
INSERT INTO `moe_payment`.`moe_plan_feature_relation` (`level`, `code`, `allow_type`, `enable`, `quota`)
VALUES (1201, 'payroll2', 2, 1, -1);


#老数据迁移
INSERT INTO moe_business.moe_staff_payroll_setting
  (business_id, staff_id,
   service_commission_enable, service_pay_rate, addon_pay_rate,
   hourly_commission_enable, hourly_pay,
   tips_commission_enable, tips_pay_rate)
SELECT business_id, id, IF(pay_by = 0, true, false), service_pay_rate, addon_pay_rate, IF(pay_by = 1, true, false),
       hourly_pay_rate, true, tips_pay_rate
FROM moe_business.moe_staff; # 全量迁移
# where business_id in (100000); #填需要开通的商家的 businessId

#（暂不使用）白名单方案：先开白名单，再迁数据
insert into moe_business.moe_business_payroll_setting (business_id) values (100000); # 填需要开通的商家的 businessId
UPDATE moe_business.moe_business_payroll_setting set new_payroll_enable = true where business_id in (100000); # 填需要开通的商家的 businessId

#（暂不使用） 后续全量开通：修改默认值，刷存量数据，暂不使用，默认全部开启
ALTER TABLE moe_business.moe_business_payroll_setting MODIFY COLUMN new_payroll_enable bool NOT NULL DEFAULT true COMMENT 'new payroll setting enable, default true';
UPDATE moe_business.moe_business_payroll_setting set new_payroll_enable = true;
