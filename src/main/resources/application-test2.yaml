spring:
  config:
    import:
      - "aws-secretsmanager:moego/testing/datasource?prefix=secret.datasource.default."
      - "aws-secretsmanager:moego/testing/datasource/grooming?prefix=secret.datasource.grooming."
      - "aws-secretsmanager:moego/testing/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/testing/mq?prefix=secret.mq."
      - "aws-secretsmanager:moego/testing/growthbook?prefix=secret.growthbook."
moego:
  messaging:
    pulsar:
      tenant: test2
  daily-report:
    client-url: https://client.t2.moego.dev/daily/report/%s
