package com.moego.svc.online.booking.helper;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.service.organization.v1.BatchGetTaxRuleRequest;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc;
import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/16
 */
@Component
@RequiredArgsConstructor
public class TaxHelper {

    private final TaxRuleServiceGrpc.TaxRuleServiceBlockingStub taxStub;

    /**
     * List tax by ids.
     *
     * @param taxIds tax ids
     * @return tax id -> tax
     */
    public Map<Long, TaxRuleModel> listTax(Collection<? extends Number> taxIds) {
        var ids = taxIds.stream()
                .map(Number::longValue)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());

        if (ids.isEmpty()) {
            return Map.of();
        }

        var request = BatchGetTaxRuleRequest.newBuilder().addAllIds(ids).build();

        var response = taxStub.batchGetTaxRule(request);
        return response.getRulesList().stream()
                .collect(Collectors.toMap(TaxRuleModel::getId, Function.identity(), (o, n) -> o));
    }
}
