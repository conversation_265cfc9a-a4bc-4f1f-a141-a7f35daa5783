package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class IdogcamConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column idogcam_config.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column idogcam_config.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column idogcam_config.kennel_id
     *
     * @mbg.generated
     */
    private String kennelId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column idogcam_config.erp_code
     *
     * @mbg.generated
     */
    private String erpCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column idogcam_config.is_active
     *
     * @mbg.generated
     */
    private Boolean isActive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column idogcam_config.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column idogcam_config.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column idogcam_config.business_id
     *
     * @mbg.generated
     */
    private Long businessId;
}
