package com.moego.svc.appointment.mapper.mysql;

import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.mapper.typehandler.ServiceOverrideTypeTypeHandler;
import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MoeGroomingPetDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<MoeGroomingPetDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, groomingId, petId, staffId, serviceId, serviceType, serviceTime, servicePrice, startTime, endTime, status, updateTime, scopeTypePrice, scopeTypeTime, starStaffId, packageServiceId, enableOperation, workMode, serviceColorCode, startDate, endDate, serviceItemType, lodgingId, priceUnit, specificDates, associatedServiceId, priceOverrideType, durationOverrideType, createdAt, updatedAt, quantityPerDay, dateType, totalPrice, quantity, orderLineItemId);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Integer.class)
    int insert(InsertStatementProvider<MoeGroomingPetDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MoeGroomingPetDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="grooming_id", property="groomingId", jdbcType=JdbcType.INTEGER),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.INTEGER),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.INTEGER),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.INTEGER),
        @Result(column="service_type", property="serviceType", jdbcType=JdbcType.INTEGER),
        @Result(column="service_time", property="serviceTime", jdbcType=JdbcType.INTEGER),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.BIGINT),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.BIGINT),
        @Result(column="status", property="status", jdbcType=JdbcType.TINYINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.BIGINT),
        @Result(column="scope_type_price", property="scopeTypePrice", jdbcType=JdbcType.INTEGER),
        @Result(column="scope_type_time", property="scopeTypeTime", jdbcType=JdbcType.INTEGER),
        @Result(column="star_staff_id", property="starStaffId", jdbcType=JdbcType.INTEGER),
        @Result(column="package_service_id", property="packageServiceId", jdbcType=JdbcType.INTEGER),
        @Result(column="enable_operation", property="enableOperation", jdbcType=JdbcType.BIT),
        @Result(column="work_mode", property="workMode", jdbcType=JdbcType.INTEGER),
        @Result(column="service_color_code", property="serviceColorCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="service_item_type", property="serviceItemType", jdbcType=JdbcType.TINYINT),
        @Result(column="lodging_id", property="lodgingId", jdbcType=JdbcType.BIGINT),
        @Result(column="price_unit", property="priceUnit", jdbcType=JdbcType.INTEGER),
        @Result(column="specific_dates", property="specificDates", jdbcType=JdbcType.VARCHAR),
        @Result(column="associated_service_id", property="associatedServiceId", jdbcType=JdbcType.BIGINT),
        @Result(column="price_override_type", property="priceOverrideType", typeHandler=ServiceOverrideTypeTypeHandler.class, jdbcType=JdbcType.TINYINT),
        @Result(column="duration_override_type", property="durationOverrideType", typeHandler=ServiceOverrideTypeTypeHandler.class, jdbcType=JdbcType.TINYINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="quantity_per_day", property="quantityPerDay", jdbcType=JdbcType.INTEGER),
        @Result(column="date_type", property="dateType", jdbcType=JdbcType.INTEGER),
        @Result(column="total_price", property="totalPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="quantity", property="quantity", jdbcType=JdbcType.INTEGER),
        @Result(column="order_line_item_id", property="orderLineItemId", jdbcType=JdbcType.BIGINT)
    })
    List<MoeGroomingPetDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MoeGroomingPetDetailResult")
    Optional<MoeGroomingPetDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, moeGroomingPetDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, moeGroomingPetDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default int insertSelective(MoeGroomingPetDetail row) {
        return MyBatis3Utils.insert(this::insert, row, moeGroomingPetDetail, c ->
            c.map(groomingId).toPropertyWhenPresent("groomingId", row::getGroomingId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
            .map(serviceType).toPropertyWhenPresent("serviceType", row::getServiceType)
            .map(serviceTime).toPropertyWhenPresent("serviceTime", row::getServiceTime)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(scopeTypePrice).toPropertyWhenPresent("scopeTypePrice", row::getScopeTypePrice)
            .map(scopeTypeTime).toPropertyWhenPresent("scopeTypeTime", row::getScopeTypeTime)
            .map(starStaffId).toPropertyWhenPresent("starStaffId", row::getStarStaffId)
            .map(packageServiceId).toPropertyWhenPresent("packageServiceId", row::getPackageServiceId)
            .map(enableOperation).toPropertyWhenPresent("enableOperation", row::getEnableOperation)
            .map(workMode).toPropertyWhenPresent("workMode", row::getWorkMode)
            .map(serviceColorCode).toPropertyWhenPresent("serviceColorCode", row::getServiceColorCode)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
            .map(endDate).toPropertyWhenPresent("endDate", row::getEndDate)
            .map(serviceItemType).toPropertyWhenPresent("serviceItemType", row::getServiceItemType)
            .map(lodgingId).toPropertyWhenPresent("lodgingId", row::getLodgingId)
            .map(priceUnit).toPropertyWhenPresent("priceUnit", row::getPriceUnit)
            .map(specificDates).toPropertyWhenPresent("specificDates", row::getSpecificDates)
            .map(associatedServiceId).toPropertyWhenPresent("associatedServiceId", row::getAssociatedServiceId)
            .map(priceOverrideType).toPropertyWhenPresent("priceOverrideType", row::getPriceOverrideType)
            .map(durationOverrideType).toPropertyWhenPresent("durationOverrideType", row::getDurationOverrideType)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(quantityPerDay).toPropertyWhenPresent("quantityPerDay", row::getQuantityPerDay)
            .map(dateType).toPropertyWhenPresent("dateType", row::getDateType)
            .map(totalPrice).toPropertyWhenPresent("totalPrice", row::getTotalPrice)
            .map(quantity).toPropertyWhenPresent("quantity", row::getQuantity)
            .map(orderLineItemId).toPropertyWhenPresent("orderLineItemId", row::getOrderLineItemId)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default Optional<MoeGroomingPetDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, moeGroomingPetDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default List<MoeGroomingPetDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, moeGroomingPetDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default List<MoeGroomingPetDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, moeGroomingPetDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default Optional<MoeGroomingPetDetail> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, moeGroomingPetDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(MoeGroomingPetDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(groomingId).equalTo(row::getGroomingId)
                .set(petId).equalTo(row::getPetId)
                .set(staffId).equalTo(row::getStaffId)
                .set(serviceId).equalTo(row::getServiceId)
                .set(serviceType).equalTo(row::getServiceType)
                .set(serviceTime).equalTo(row::getServiceTime)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(startTime).equalTo(row::getStartTime)
                .set(endTime).equalTo(row::getEndTime)
                .set(status).equalTo(row::getStatus)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(scopeTypePrice).equalTo(row::getScopeTypePrice)
                .set(scopeTypeTime).equalTo(row::getScopeTypeTime)
                .set(starStaffId).equalTo(row::getStarStaffId)
                .set(packageServiceId).equalTo(row::getPackageServiceId)
                .set(enableOperation).equalTo(row::getEnableOperation)
                .set(workMode).equalTo(row::getWorkMode)
                .set(serviceColorCode).equalTo(row::getServiceColorCode)
                .set(startDate).equalTo(row::getStartDate)
                .set(endDate).equalTo(row::getEndDate)
                .set(serviceItemType).equalTo(row::getServiceItemType)
                .set(lodgingId).equalTo(row::getLodgingId)
                .set(priceUnit).equalTo(row::getPriceUnit)
                .set(specificDates).equalTo(row::getSpecificDates)
                .set(associatedServiceId).equalTo(row::getAssociatedServiceId)
                .set(priceOverrideType).equalTo(row::getPriceOverrideType)
                .set(durationOverrideType).equalTo(row::getDurationOverrideType)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(quantityPerDay).equalTo(row::getQuantityPerDay)
                .set(dateType).equalTo(row::getDateType)
                .set(totalPrice).equalTo(row::getTotalPrice)
                .set(quantity).equalTo(row::getQuantity)
                .set(orderLineItemId).equalTo(row::getOrderLineItemId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MoeGroomingPetDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(groomingId).equalToWhenPresent(row::getGroomingId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(serviceId).equalToWhenPresent(row::getServiceId)
                .set(serviceType).equalToWhenPresent(row::getServiceType)
                .set(serviceTime).equalToWhenPresent(row::getServiceTime)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(scopeTypePrice).equalToWhenPresent(row::getScopeTypePrice)
                .set(scopeTypeTime).equalToWhenPresent(row::getScopeTypeTime)
                .set(starStaffId).equalToWhenPresent(row::getStarStaffId)
                .set(packageServiceId).equalToWhenPresent(row::getPackageServiceId)
                .set(enableOperation).equalToWhenPresent(row::getEnableOperation)
                .set(workMode).equalToWhenPresent(row::getWorkMode)
                .set(serviceColorCode).equalToWhenPresent(row::getServiceColorCode)
                .set(startDate).equalToWhenPresent(row::getStartDate)
                .set(endDate).equalToWhenPresent(row::getEndDate)
                .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
                .set(lodgingId).equalToWhenPresent(row::getLodgingId)
                .set(priceUnit).equalToWhenPresent(row::getPriceUnit)
                .set(specificDates).equalToWhenPresent(row::getSpecificDates)
                .set(associatedServiceId).equalToWhenPresent(row::getAssociatedServiceId)
                .set(priceOverrideType).equalToWhenPresent(row::getPriceOverrideType)
                .set(durationOverrideType).equalToWhenPresent(row::getDurationOverrideType)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(quantityPerDay).equalToWhenPresent(row::getQuantityPerDay)
                .set(dateType).equalToWhenPresent(row::getDateType)
                .set(totalPrice).equalToWhenPresent(row::getTotalPrice)
                .set(quantity).equalToWhenPresent(row::getQuantity)
                .set(orderLineItemId).equalToWhenPresent(row::getOrderLineItemId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_pet_detail")
    default int updateByPrimaryKeySelective(MoeGroomingPetDetail row) {
        return update(c ->
            c.set(groomingId).equalToWhenPresent(row::getGroomingId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .set(serviceType).equalToWhenPresent(row::getServiceType)
            .set(serviceTime).equalToWhenPresent(row::getServiceTime)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(scopeTypePrice).equalToWhenPresent(row::getScopeTypePrice)
            .set(scopeTypeTime).equalToWhenPresent(row::getScopeTypeTime)
            .set(starStaffId).equalToWhenPresent(row::getStarStaffId)
            .set(packageServiceId).equalToWhenPresent(row::getPackageServiceId)
            .set(enableOperation).equalToWhenPresent(row::getEnableOperation)
            .set(workMode).equalToWhenPresent(row::getWorkMode)
            .set(serviceColorCode).equalToWhenPresent(row::getServiceColorCode)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .set(endDate).equalToWhenPresent(row::getEndDate)
            .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
            .set(lodgingId).equalToWhenPresent(row::getLodgingId)
            .set(priceUnit).equalToWhenPresent(row::getPriceUnit)
            .set(specificDates).equalToWhenPresent(row::getSpecificDates)
            .set(associatedServiceId).equalToWhenPresent(row::getAssociatedServiceId)
            .set(priceOverrideType).equalToWhenPresent(row::getPriceOverrideType)
            .set(durationOverrideType).equalToWhenPresent(row::getDurationOverrideType)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(quantityPerDay).equalToWhenPresent(row::getQuantityPerDay)
            .set(dateType).equalToWhenPresent(row::getDateType)
            .set(totalPrice).equalToWhenPresent(row::getTotalPrice)
            .set(quantity).equalToWhenPresent(row::getQuantity)
            .set(orderLineItemId).equalToWhenPresent(row::getOrderLineItemId)
            .where(id, isEqualTo(row::getId))
        );
    }
}