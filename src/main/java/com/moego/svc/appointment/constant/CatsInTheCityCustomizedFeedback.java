package com.moego.svc.appointment.constant;

import com.moego.idl.models.appointment.v1.QuestionCategoryType;
import com.moego.idl.models.appointment.v1.QuestionDef;
import com.moego.idl.models.appointment.v1.QuestionType;
import java.util.List;

/**
 * 时间原因临时给 CatsInTheCity 商家手动配置一些 customized feedback
 * 后续 GA 版本会删除, 并转移至 DB 中配置
 */
public interface CatsInTheCityCustomizedFeedback {
    List<QuestionDef> CATS_IN_THE_CITY_CUSTOMIZED_FEEDBACK_CONTENTS = List.of(
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("daily_wellness_report")
                    .setTitle("Daily Wellness Report")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "Bright, active, and alert, showing curiosity and engagement with their surroundings.",
                                    "Has a healthy appetite and is drinking water, maintaining normal hydration and nutrition.",
                                    "Is using the litter box normally, with regular and healthy habits.",
                                    "Has a decreased appetite, and we are monitoring closely to ensure well-being.",
                                    "Presents as shy, preferring a gentle and patient approach to interaction.",
                                    "<PERSON> is active and engaged, with a healthy appetite and normal litter box use.",
                                    "<PERSON> is active and engaged, showing curiosity and playfulness, with a healthy appetite.",
                                    "<PERSON> is active and engaged, but currently has a light appetite.",
                                    "<PERSON> is active and engaged, but right now lacks an appetite. We are monitoring closely.",
                                    "Kitty is shy, responding well to one-on-one time, with a healthy appetite and normal litter box habits.",
                                    "<PERSON> is shy and responds well to one-on-one time, with a healthy appetite.",
                                    "Kitty is shy and responds well to one-on-one time, though currently has a light appetite.",
                                    "Kitty is shy and lacks an appetite at the moment. We are monitoring closely.",
                                    "Kitty is shy and prefers alone time, maintaining a healthy appetite and normal litter box habits.",
                                    "Kitty is shy and prefers alone time, with a healthy appetite.",
                                    "Kitty is shy and prefers alone time, currently showing a light appetite.",
                                    "Kitty is shy and prefers alone time, and we are monitoring closely.",
                                    "We are monitoring Kitty’s wellness and behaviors closely, ensuring their comfort and well-being."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("first_day_at_cats_in_the_city")
                    .setTitle("First day at cats in the city")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "Kitty cat was active and social, enjoyed their room and attention from the team.",
                                    "We are very excited to see kitty again!",
                                    "Kitty cat ate well, used the litter box, and easily adjusted to boarding.",
                                    "Kitty settled in, was relaxed, and took naps.",
                                    "Kitty cat was shy but seemed content.",
                                    "Kitty cat was nervous but found comfort in the cubby and/or climbing structures.",
                                    "Kitty cat took their time settling in, and found comfort with their basket, bedding, and other cozy items in the room."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("attitude_and_demeanor_while_boarding")
                    .setTitle("Attitude and Demeanor While Boarding")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "active",
                            "assertive",
                            "calm",
                            "charming",
                            "cuddly",
                            "confident",
                            "chipper",
                            "lap cat",
                            "enthusiastic",
                            "friendly",
                            "fussy",
                            "good-natured",
                            "happy-go-lucky",
                            "hold me!",
                            "jolly",
                            "mellow",
                            "obedient",
                            "pet me?",
                            "pet me.",
                            "pet me!",
                            "playful",
                            "pleasant",
                            "polite",
                            "quiet",
                            "serious",
                            "shy",
                            "sooo cute!",
                            "beautiful",
                            "adorable",
                            "sweet",
                            "happy",
                            "sociable",
                            "independent",
                            "observant",
                            "content",
                            "smiley",
                            "popular",
                            "angelic",
                            "clever",
                            "mischievous",
                            "energetic",
                            "silly",
                            "grouchy",
                            "affectionate",
                            "talkative",
                            "exuberant",
                            "well-behaved",
                            "well-mannered",
                            "easy-going",
                            "curious",
                            "goofy",
                            "relaxed",
                            "hold me?",
                            "fun-loving",
                            "sleepy",
                            "joyful",
                            "social butterfly",
                            "Mr. Popular",
                            "Ms. Popular",
                            "life of the party",
                            "everybody's best friend",
                            "loves human attention",
                            "proud",
                            "sassy",
                            "swaggering",
                            "me first!",
                            "missing you (getting lots of affection from us)",
                            "regal",
                            "aloof",
                            "suave",
                            "bubbly",
                            "wrapped up in thought",
                            "is it lunch time yet!?",
                            "good citizen",
                            "busy",
                            "cheerful",
                            "dainty",
                            "lively",
                            "loving",
                            "loves being cozy",
                            "loves looking out the window",
                            "bird watcher",
                            "merry",
                            "outgoing",
                            "nice",
                            "sensitive",
                            "wild",
                            "wiggly",
                            "spirited",
                            "wonderful"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("a_kitty_has_to_eat")
                    .setTitle("A Kitty Has to Eat!")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Kitty Cat is very food motivated, showing enthusiasm during mealtime.",
                            "Kitty Cat has a healthy appetite, eating well and maintaining good nutrition.",
                            "Kitty has started to eat well after initially skipping the first few meals, showing improvement.",
                            "Kitty Cat is food restricting, and we are monitoring closely, offering yummy options to encourage eating.",
                            "Kitty Cat food restricted, and we intervened as necessary with food motivators, hydration, and manual feeding.",
                            "Kitty Cat food restricted, requiring medical intervention, but has resumed eating!",
                            "Kitty Cat seems prone to food restriction, and we are maintaining close observation.",
                            "Kitty Cat occasionally gorges and purges, and we are monitoring feeding behavior.",
                            "Kitty seems to be a social eater, responding positively to companionship during meals.",
                            "Kitty eats best when we sit with them, enjoying social meal times with staff.",
                            "Kitty is at risk of dehydration, and we are providing subcutaneous fluids and HydraCare to support hydration.",
                            "Kitty is uninterested in their own food, so we are providing a variety of options to encourage eating.",
                            "Kitty has a lighter appetite right now, but is eating. We are monitoring closely."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("meet_me_at_the_litter_box")
                    .setTitle("Meet Me at the Litter Box")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Kitty cat uses the litter box perfectly.",
                            "Kitty cat urinates regularly and never misses the box. Kitty has practiced their aim.",
                            "Kitty cat is having occasional accidents that are manageable.",
                            "Kitty cat is having some accidents and is responding well to wee-pads.",
                            "Kitty cat likes to party in and out of the litter box.",
                            "Kitty cat engages in marking behavior.",
                            "Kitty has developed loose stools, or diarrhea",
                            "Kitty cat presents as constipated",
                            "Stool present in the litter box.",
                            "Urine present in the litter box.",
                            "Kitty's loose stool is clearing up!",
                            "No stool present",
                            "No urine present"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("how_regular_would_you_say")
                    .setTitle("How Regular Would You Say?")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Kitty cat has normal appearing and regularly occurring bowel movements.",
                            "Kitty cat has normal appearing bowel movements.",
                            "Kitty cat has loose stools. Please monitor elimination upon returning home.",
                            "Kitty cat has occasional solid waste accidents that are manageable.",
                            "Kitty cat is on strike and is refusing to use the litter box."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("playtime_happy_hour_and_catio")
                    .setTitle("Playtime, Happy Hour, and Catio!")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "Kitty Cat loved their private room, finding comfort and relaxation in their own space.",
                                    "Snuggle Bros! An absolute cuddle champion, enjoying grooming time and snuggling up in the coziest kitty pile.",
                                    "Kitty Guest Coordinator: Always checking in on and \"chatting\" with fellow kitty guests—our resident gossip!",
                                    "Chef’s Little Assistant: A dedicated kitchen director during mealtime prep, with aspirations of being promoted to dishwasher!",
                                    "Office Assistant Extraordinaire: A natural desk ornament, specializing in rearranging paperwork and redefining work priorities.",
                                    "Kitty Cat loved Catio time, basking in the fresh air and enjoying outdoor views.",
                                    "Kitty loved exploring the house, making the most of their time outside their room with curiosity and adventure.",
                                    "Kitty Cat enjoyed the common areas and Catios, finding joy in the shared spaces and outdoor enclosures.",
                                    "Kitty Cat the Explorer: Regularly toured the building and Catios, always entertained by their daily adventures.",
                                    "Kitty Cat was initially nervous about the common areas and Catio but gained confidence with each new experience.",
                                    "Kitty Cat preferred the tranquility of their private bedroom, finding the common areas and Catio a bit too stimulating.",
                                    "Kitty Cat took the gold in competitive napping, mastering the art of rest and relaxation!",
                                    "Kitty cat was a chatty Cathy and has lots of stories to tell. Some stories were clearly embellished but we listened with great suspense.",
                                    "Kitty cat loves catnip. Kitty cat was active and had great fun playing with catnip during happy hour!",
                                    "Kitty cat seemed to have a very active nightlife, often making their room look like they partied all night long",
                                    "Kitty cat thrived with 1-on-1 attention and enjoyed the time we shared together.",
                                    "Kitty cat has many stories to tell, loves to chat it up, and finds most any interaction worthy of comment. Meow, Meow, Meow!",
                                    "Kitty cat loves toys. Toys, toys, and more toys.",
                                    "Kitty made a new friend today!",
                                    "Window Watching: Relaxing by the window and enjoying the sights of birds, trees, and the outside world.",
                                    "Cuddling with Staff: Snuggling up with our caring team for warmth, love, and gentle pets.",
                                    "Interactive Play: Engaging in fun games with wand toys, teasers, and other interactive toys.",
                                    "Snuggle Time: Getting cozy with soft blankets and enjoying quiet, comforting moments.",
                                    "Lounging on the Cat Tree: Perching high above and observing the surroundings from a safe and comfortable spot.",
                                    "Exploring the Playroom: Roaming around the safe, enriching play space filled with toys and obstacles.",
                                    "Music Time: Unwinding with calming tunes that create a soothing environment.",
                                    "Enjoying Catnip: A little sprinkle of catnip to bring out playfulness and joy.",
                                    "Storytime: Listening to soft, calming stories read by our staff.",
                                    "Playing with Textures: Exploring crinkle tunnels, scratching posts, and different tactile experiences.",
                                    "Sunbathing: Finding a sunny spot and soaking up the warm, natural light.",
                                    "Cozy Bed Time: Curling up in a warm and plush bed for a peaceful nap.",
                                    "Puzzle Play: Challenging the mind with enrichment toys that reward curiosity and problem-solving.",
                                    "Sensory Enrichment: Enjoying safe, pet-friendly scents and gentle sounds for mental stimulation.",
                                    "Bird Watching: Observing wildlife from a safe indoor spot, providing mental engagement and curiosity.",
                                    "Massage Time: Receiving gentle massages and head scratches from staff for relaxation.",
                                    "Treat Tester Extraordinaire: Always ready to sample the latest snacks and offer a discerning (and enthusiastic) opinion.",
                                    "Head Biscuit Baker: Master of the kneading technique, baking up the fluffiest \"biscuits\" with those talented paws.",
                                    "Official Toy Tester: Takes playtime seriously, ensuring every toy passes the fun and durability test.",
                                    "Squirrel Enthusiast: Expert at keeping a watchful eye on the local wildlife, especially the squirrel squad.",
                                    "Counter Surfer: Embraces the thrill of mischief by darting across kitchen counters with stealth and speed.",
                                    "Purr Master: A true virtuoso of purring, providing soothing, melodic vibrations to everyone nearby."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("medication_administration")
                    .setTitle("Medication Administration")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of("Medication was administered successfully."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("checkout_last_day_at_cats_in_the_city")
                    .setTitle("Checkout | Last Day at Cats in the City")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Kitty cat did not eat breakfast and will need to eat immediately upon returning home.",
                            "Kitty had a relaxing morning, and is excited to see you again.",
                            "Kitty cat was administered their medication as directed on the day of checkout.",
                            "Kitty cat was active, toured the building, and used the Catio.",
                            "Kitty had fun touring the building and saying goodbye to us before going home.",
                            "Kitty loved their stay at Cats in the City and can't wait to come back again.",
                            "We look forward to meeting again!"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("tandem_cat_grooming_report_initial_presentation_and_assessment")
                    .setTitle("TANDEM Cat® Grooming Report | Initial Presentation and Assessment")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Office Visit with Bath and Nail Trim",
                            "Pre-existing wound discovered during grooming session",
                            "Office Visit with No Bath",
                            "Matting",
                            "Felted",
                            "Compacted Hair",
                            "Skin Damage from Mats",
                            "Bruising Patterns and skin discoloration from mats",
                            "Hair Loss, Thinning, and/or Bald Patches",
                            "Greasy Coat; contributes to mat formation, adds texture and tackiness to the coat",
                            "Body Odors",
                            "Urine / Fecal Waste in Coat",
                            "Urine Burn / Fecal Scald",
                            "Tar, sap, or other matter in the coat",
                            "Dandruff",
                            "Stud Tail (Greasy Tail)",
                            "Dermatitis",
                            "Possible Acne",
                            "Rule Out Infection",
                            "Fleas",
                            "Introductory Office Visit"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("grooming_information")
                    .setTitle("Grooming Information")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "De-Shedding: Indoor cats shed all year long, and there are usually two periods of increased shedding a year: once in the spring and again in the fall. When a shed season is allowed to stay in the coat, it compounds with the next shed season forcing mats and dreadlocks. A de-shedding service helps maintain the health of the undercoat by thinning the undercoat, sometimes significantly, to manage and prevent the formation of mats and dreadlocks in the coat.",
                                    "The undercoats of some breeds require more frequent de-sheddings to prevent mats and dreadlocks, including those of Persians, Himalayans, Maine coons, Exotics, Birman, Ragamuffin, Ragdolls, Turkish Vans, Norwegian Forest Cats, Siberians, Bobtails, and many mixed breed cats. The undercoats of senior cats and obese cats, and cats with particularly oily skin or dandruffy skin can also require more frequent de-sheddings.",
                                    "DE-SHEDDINGS MAKE BATHING MORE EFFECTIVE because a full dense coat, a coat with mats, or an oily coat will clump and mat further once wet. A thick undercoat must be thinned and all masses in the coat removed prior to a saturating bath to avoid subsequent matting.",
                                    "De-greasing: Oils produced in the skin accumulate on the skin and in the undercoats of cats. Body oils are terrific for skin and coat health. However, when oils accumulate and remain on the skin or in the coat, the skin can dry out causing dandruff, scabbing, itchiness, and alopecia (hair loss). Oily coats hold shed hair and dust, causing mats and compacted coats. Conditions such as Seborrhea dermatitis and stud tail are caused by the accumulation of oils on the skin and in the coat.",
                                    "Fleas: It is our firm policy to flea dip any cat upon discovery of live fleas. The flea dip is done prior to the traditional water bath and has no protective features once washed off and therefore is no replacement for flea prevention. A flea dip works on contact to neutralize any fleas present in the coat on contact. Importantly, flea dips neutralize all life stages of fleas (adults, eggs, and larvae).",
                                    "NOTE ON FLEAS: If a cat has fleas then the physical environment that the cats spends time in will also have fleas. It is important to advise clients of this fact. Recommend Hot Shot Flea Bombs and Knockout spray to treat the home for fleas. Also recommend washing bedding, vacuuming regularly, and keeping an eye out for flea dirt. Flea dipping a cat and adding a flea preventative is not enough to keep cats flea-free if the physical space maintains a flea colony. The space must be dealt with",
                                    "De-matting: A service that uses specialized tools to remove mats from the coat while maintaining the full length coat. This service is usually provided in conjunction with a de-shedding because typically if there are mats present, the undercoat is too full and requires thinning. Note that De-matting cannot be done on all matted coats. Some coats require shaving if mats are too extensive for a de-matting service, which again preserves the overall length of the coat.",
                                    "Pin mats - Small mats that form commonly on the head and neck. If you look at the top of a cat’s head and you see separated fur that looks like its in small tufts, those are pin mats. Usually pin mats are easily removed.",
                                    "Ball mats - large knots that form ball-like masses in the undercoat. We can usually remove these safely while preserving the overall length of the coat.",
                                    "Dreadlocks - spindly tangles that form in the coat, commonly around the ears and mane, but can occur anywhere.",
                                    "Carpet - Flatter, longer mats that form along the skin. These mats sometimes require shaving to remove. Carpet mats are large mats.",
                                    "Felted - when Carpet mats interconnect and form a layer of fabric like fur over the cats skin. A felted cat usually requires shaving to void the mats. The coat usually has to start over.",
                                    "Compacted fur - loose fur that gets trapped in the undercoat and impacts against the skin. Compacted fur is not a mat. It is the precursor to mats and sometimes the compacted fur never actually forms a mat. However, compacted fur still needs to be removed from a coat in order to avoid hair loss, skin issues, and likely matting. Compacted fur can be deceiving and sometimes is not obvious until the grooming session is underway. Compacted fur can be removed to preserve the overall length of the coat.",
                                    "De-skunk: a product used to remove strong odors such as skunk spray and tobacco. Note that some odor often lingers and we cannot de-skink the head. De-skunking reduces odor significantly, but often does not eliminate the odor completely.",
                                    "Tooshy: Sanitary Trim Standard: cleans up the back end of the cat. The anus and urethra opening are cleared of any occluding fur by shaving the fur short around these areas. Important for helping cats keep tidy.",
                                    "Belly: Belly shave. Sometimes it’s helpful to shave a cat’s belly for ease of maintenance and temperature regulation in the summertime. Standard: Shaves the inside of the hind legs and belly beneath the diaphragm. Aggressive: Shaves the inside of the hind legs, belly, and chest into the armpits. This is useful for cats with matting issues on underside and in armpits.",
                                    "Stud tail: An oily patch of skin and fur that occurs on the base of the tail, dorsal side. Oil glands in the area become overproductive, leading to a qualitative change in the fur and skin texture in the area. Stud tail is usually not a serious condition. However, in some cases stud tail can lead to dry, flaky skin, skin infections, and alopecia. Treatment includes bathing with a de-greaser to thin the excess oils.",
                                    "Seborrheic dermatitis: Seborrhea refers to a coat that is particularly oily or greasy. Seborrheic coats often have copious amounts of dandruff. Bathing using a benzoyl peroxide base shampoo and conditioner is the treatment for most cases of seborrhea. Aging, obesity, diabetes, and hypothyroid are risk factors for developing seborrhea.",
                                    "Flea dermatitis: Refers to skin that has an allergic response to fleas. Commonly, bumps and scabs form on a cat’s back in response to fleas. Alopecia might also be present. Voiding fleas from the coat is necessary to correct flea dermatitis.",
                                    "Ingrown nail: When nails grow long enough that they curl back, puncturing the paw pads. Usually the treatment for ingrown nails is clipping the nails and cleansing wounds caused by the ingrown nail. In extreme cases a veterinarian might be needed, especially if stitches are necessary.",
                                    "Hairballs: When cats ingest too much hair, their esophagus can fill with hair, which can lead to hair regurgitation, or hairballs. Reducing the fur available to swallow is one solution for controlling hairballs. Desheddings to remove loose hair from the coat is a great solution for managing hairballs. If hairballs persist, then shaving might be necessary to control hairballs. Also, daily hairball treats can also be very helpful for reducing hairballs.",
                                    "Hyperthyroid: Your thyroid produces thyroid hormone (TH). TH controls your rate of metabolism. In hyperthyroid, too much TH is produced leading to an overactive metabolic rate. Cats with hyperthyroid often present as thin with particularly oily coats. Excess thirst and increasing vocalizations are also signs of hyperthyroidism. Because cats develop oily coats in this condition, the groomer is sometimes the first professional to suggest the cat be tested for hyperthyroid. Methimazole is a possible treatment option.",
                                    "Diaper rash and urine scald: Cats can develop rashes on their hindquarters when fecal matter and urine are allowed to sit in the coat. Longhaired, overweight, and elderly cats are especially susceptible to hygiene issues. Sanitary trims will help prevent waste buildup in the coat. Wiping the kitty's bottom daily will also help keep the area clean.",
                                    "Ringworm: Ringworm is a non-life threatening superficial fungal infection of the skin. Ringworm is highly contagious between cats and between people and cats. If ringworm is suspected, a groom must stop immediately and owners are notified. Treatment for ringworm includes prescription oral antifungal medication and “sulfur dips.” Sulfur dips are baths that use a sulfur based shampoo to void skin fungus. We cannot perform sulfur dips because we cannot work with cats with ringworm. Most stray cats have ringworm."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("departure_notes")
                    .setTitle("Departure Notes")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Moisture remains in the coat",
                            "Snazziest Tuxedo of the Day Award!",
                            "Paws may be damp",
                            "Expect more shedding over the next few days",
                            "Dander remains in the coat; it should continue to exfoliate over the next few days",
                            "Superficial Quick cut during claw trim. Minor and nothing more needs to be done.",
                            "De-shedding twice a year will likely keep kitty mostly free of matting.",
                            "Flea bodies remain the coat; they will fall out over the next few hours or days",
                            "Kitty said \"No!\" to final brush out",
                            "Kitty did not like us working with limbs or around head so those areas are bit wonky",
                            "Best Conversationalist Award!",
                            "Wonderful Cat",
                            "Fluffiest Cat of the Day Award!",
                            "Most Beautiful Eyes of the Day Award!",
                            "Best cat of the day award!",
                            "The Cutest Cat of the Day Award!",
                            "Friendliest Cat of the Day Award!",
                            "Strongest Cat of the Day Award!",
                            "Bravest Cat of the Day Award!",
                            "Sweetest Cat of the Day Award!",
                            "Delightful kitty!",
                            "Always welcome!",
                            "Best Cha Cha Cha of the Day Award!",
                            "Most Unique Meow of the Day Award!",
                            "Best Tail of the Day Award!",
                            "Best Ear Tufts of the Day Award!",
                            "Best Paw Tufts of the Day Award!",
                            "Best Fluff of the Day Award!",
                            "Softest Coat of the Day Award!",
                            "Cuddle Champion of the Day Award!",
                            "Most Magnificent Mane of the Day Award",
                            "Prettiest Smile of the Day Award!",
                            "Claw Sharpener Extraordinaire of the Day Award!",
                            "Cat Nap Champion of the Day Award!",
                            "Best Helper of the Day Award!",
                            "Best Bath-time Cat of the Day Award!",
                            "Best Tongue Blip of the Day Award!"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("grooming_statements")
                    .setTitle("Grooming Statements")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "Shed Cycles | Even though cats shed all year long, cats generally have two periods of peak shed per year, once during the Spring and again in the Fall. Timing grooming sessions for the Easter and Halloween periods often results in the most amount of fur being able to removed from the undercoat because the undercoat is ready to be thinned.",
                                    "The coat still contains an excess of skin oils. We provided several degreasing baths today and were able to significantly reduce the amount of oil in the coat. Further bathing will be necessary to reduce the oil content further. Please let us know if you would like to schedule a bathing-only appointment to further thin skin oils.",
                                    "We provided the longest haircut possible based on undercoat density, skin condition, and fur quality.",
                                    "Cats have anal glands - two pea-size sacks located near your cats anus. The glands produce a viscous solution that confer a unique scent to a cat's excrement. Occasionally the glands become overly full with viscous solution, either because the glands get clogged or overly and become overly full. back up with solution and become impacted. We check the glands of cats in our grooming program to guard against impaction.",
                                    "Felted Fur | Without human intervention mats can lead to felted fur. Felted fur results when individual fur strands become interwoven like a sheet of wool over areas of the body. The only way to relieve felted fur is usually by shaving the fur. Felted fur is uncomfortable, can decrease a cat's mobility, and can lead to skin and fur damage.",
                                    "Felted Hair | Without human intervention matting will progress into felting. Felted hair results when the hair has become woven like a sheet of felt or wool. This is generally uncomfortable, puts tension on the skin, decreases mobility, causes skin damage, and can lead to infection.",
                                    "Fleas are a flightless parasite that live off the red blood cells of animals and people. Fleas can transmit diseases to their host and can cause allergic reaction and flea dermatitis. It is often necessary to use a flea preventative for extended periods of time or indefinitely to prevent fleas from populating a cat’s coat.",
                                    "Cat Acne | It most commonly develops under the chin and can present as black speckling, hair thinning, or patches of skin irritation.",
                                    "We degreased the tail, but we do not want to over scrub the area because we can dislodge too much fur leaving the area bald. To manage Stud Tail, please consider purchasing degreasing wipes from Groomers Goo and wipe the tail every few days to mitigate the accumulation of oils in the area. While very useful, Groomer's Goo pads do have a scent. You can also use ear wipes on the tail which usually do not have a scent."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("hair_damage_skin_injuries_other_natural_consequences_of_matting")
                    .setTitle("Hair Damage, Skin Injuries & Other Natural Consequences of Matting")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "You might notice skin and fur damage from matting. Grooming does not cause skin damage. Grooming can make pre-existing skin damage and fur thinning caused by mats evident. Matting creates tension on fur follicles and creates pressure points. Like a ponytail tied too tightly for too long, the skin can become very irritated from tension and pressure caused by mats. Now that the tension created by the mats on the fur follicles is relieved, the skin and fur can be expected to resolve itself quickly.",
                                    "Areas of high mobility such as the shoulders, limbs, chin, ears, neck, hips, chest, and belly are particularly prone to signs of irritation due to matting. Further, mats occlude air flow to the skin, which can also lead to skin damage. Discolored skin, flaky or chafed skin, inflamed skin, superficial sores, and fur thinning can all result from mats.",
                                    "Sometimes a grooming session is about resolving a fur issue rather than providing the finest aesthetics. The outcome of today’s grooming session is a functional outcome. Future grooming sessions can focus on aesthetics."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("fleas")
                    .setTitle("Fleas")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "Grooming alone will not solve a flea infestation. We’ve done our part. The rest is up to you! Flea treatments are not guaranteed and repeated grooming sessions may be required to fully solve your flea infestation.",
                                    "https://catsinthecity.com/fleas/",
                                    "If a cat has fleas then the physical environment that the cats spends time in will also have fleas. It is important to advise clients of this fact. Recommend Hot Shot Flea Bombs and Knockout spray to treat the home for fleas. Also recommend washing bedding, vacuuming regularly, and keeping an eye out for flea dirt. Flea dipping a cat and adding a flea preventative is not enough to keep cats flea-free if the physical space maintains a flea colony. The space must be dealt with",
                                    "Fleas: It is our firm policy to flea dip any cat upon discovery of live fleas. The flea dip is done prior to the traditional water bath and has no protective features once washed off and therefore is no replacement for flea prevention. A flea dip works on contact to neutralize any fleas present in the coat on contact. Importantly, flea dips neutralize all life stages of fleas (adults, eggs, and larvae).",
                                    "The source of the fleas in your home must be eradicated. Fleas can travel from neighbors who live upstairs, downstairs, and next door. Fleas can live in yards, basements, crawlspaces, attics, walls, bedding, litter boxes, and voids created by furniture. While treating your home yourself with home foggers and sprays is possible, remember that fleas survived the comet and fleas are likely to survive your efforts as well. A licensed exterminator is most skilled and best equipped to solve flea infestations.",
                                    "If you see live fleas on your cat after today’s grooming appointment, your flea infestation is much bigger than grooming can solve. Your flea infestation will need to be solved by a licensed exterminator and a veterinarian."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("temperament")
                    .setTitle("TEMPERAMENT")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Loves Spa Day",
                            "Would have rather stayed in bed, and deserves treats later",
                            "Agreeable and Cooperative",
                            "Expressed some opinions and told lots of stories",
                            "Nervous and benefitted from lots of reassurance",
                            "Sound sensitive",
                            "Wiggly",
                            "Wandered, lots of activity",
                            "Bouncy, jumped without warning",
                            "Grumpy, but deserves a treat later",
                            "Shy and Reserved",
                            "Speak with your vet about a sedative",
                            "The sedative supplied by you was a success",
                            "Sensitive about us working around the head",
                            "Sensitive about us working with and touching limbs"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("care_suggestions")
                    .setTitle("Care Suggestions")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "Promote healing with the use of Vetericyn antimicrobial spray for cats. Vetericyn is an over-the-counter product used to treat minor wounds and skin irritation. Vetericyn is a bit like Neosporin for humans in that it disinfects, soothes, and promotes healing. Vetericyn is available in most pet stores and online.",
                                    "To help mitigate dandruff production due to oily coats, consider using Douxo S3 SEB at home. Douxo S3 SEB is a leave-in moose product that helps reduce dandruff in oily and seborrheic coats.",
                                    "Nail coatings such as Soft Claws should be removed after 8 weeks if they are still in place. Soft Claws can be removed with a simple nail trim. Soft Claws can be reapplied once the old Soft Claws are removed.",
                                    "If you have multiple pets at home, please observe your pets when your cat returns home. Grooming can alter the visual and scent profiles of a pet. Other pets might need time to adjust to their freshly groomed housemate.",
                                    "Reduce dandruff at home by using Bert's Bees Dander Reducing Pet Wipes.",
                                    "Promote healing with the use of Vetericyn Facial Therapy for cats. Vetericyn Facial Therapy is an over-the-counter product used to treat minor wounds, infection, and skin irritation on the head and face. Safe for use on the eyes, nose, around the mouth, and chin. Vetericyn is a bit like Neosporin for humans in that it disinfects, soothes, and promotes healing. Also useful for treating cat Chin Acne, for maintaining eye and conjunctiva health, and for cleaning eyes.",
                                    "Please consider using cat wipes or similar to keep your cat clean. Baby wipes are useful for keeping the rear end, paws, and nails clean.",
                                    "Please consider using Paw Butter or similar to maintain your cat’s paw health. Paw Butter is useful for hydrating and healing dry, cracked, and calloused paw pads.",
                                    "Please consider using a metal grooming rake to remove small mats and fur masses from your cat’s coat. Rakes are useful for dislodging small mats because they minimize tension on fur follicles thereby making it easier to remove fur masses.",
                                    "Please consider using a metal skip tooth comb with a single row of tines to thin and brush your cat’s undercoat. This tool is especially useful for maintaining the undercoat before matting or fur masses form.",
                                    "Please consider using a slicker comb with flexible tin tines for everyday brushing at home. Slicker combs are terrific for maintaining short hair coats and long hair coats before they mat. Pair with a metal skin tooth comb for deeper brushing on cats with thick undercoat or long overcoats.",
                                    "Your cat had live fleas in their coat. This means that there likely is flea activity in their environment. A flea dip is not a flea preventative. CapStar oral medication is not a flea preventative. To protect your cat from fleas in their environment, please consider maintaining your cat on a quality flea preventative such as Cheristin or Activyl. If you do not apply and maintain the flea preventative, your cat will be unprotected against fleas in their environment.",
                                    "The nail beds of your cat are likely infected. An antibiotic from a veterinarian is likely necessary to resolve the infection. We treated the nail beds with the antiseptic spray for cats Vetericyn and we encourage the use of Vetericyn on the nail beds at home to keep the area clean. The use of Vetericyn is in addition to veterinary care, not instead of. Your cat likely needs antibiotics prescribed from a veterinarian.",
                                    "Please consider having your cat’s oral health evaluated by a veterinarian. Your cat would likely benefit from a dental appointment with their veterinarian.",
                                    "Chin acne is characterized by the presence of black heads, scabbing, or fur loss on the chin. Chin acne is thought to be caused by bacteria present on plastic water and food bowls or in stagnant drinking water. If left untreated, chin acne can progress to very irritated and lesioned skin on and around the chin. Please consider treating your cat’s chin with Vetericyn facial therapy daily in addition to cleaning food and water bowls daily.",
                                    "Fur is like a fuzzy coat that helps to regulate body temperature. Now that your cat is shaved they might feel colder than usual, especially in cooler weather or in air conditioned air. It is important to help your cat feel warm. Please consider providing your cat with a sweater, heated blanket, and/or heating pad. Please consider keeping your home warmer than usual and limit kitty’s access to cold spaces such as the outdoors and catios.",
                                    "Now that your cat is shaved they might be at heightened risk of sun burn, wind burn, or other injury from outdoor elements. Please consider limiting your cat’s access to the elements for a period while their fur regrows. For instance, if you cat has a lion cut, please consider limiting access to the outdoors during extremely sunny parts of the day.",
                                    "To improve skin health and mitigate dandruff production please consider increasing the content of Omega3 fatty acids in kitty's diet. Fish oil by Welactin for cats is an excellent source of digestible Omega3s. Omega3s represent a systemic way of improving skin health.",
                                    "It's important to make sure that cats feel warm during cold weather, especially after grooming because grooming reduces fur volume. Signs that your cat feels cold include shivering, spending time near heat sources, lethargy, and decreased appetite. If your cat feels cold, help them to feel warm by providing a heating pad, electric blanket and/or a sweater. Turn your home’s heat up to 70F or warmer. Consider heating your cat’s food."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("optimized_grooming_schedule")
                    .setTitle("OPTIMIZED GROOMING SCHEDULE")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Monthly",
                            "Every Two Months",
                            "Every Three Months",
                            "Every Four Months",
                            "Every Five Months",
                            "Quarterly",
                            "Every Six Months",
                            "Every Eight Months",
                            "Every Ten Months",
                            "Spring / Fall",
                            "Summer / Winter"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("ingrown_nails_wound_care")
                    .setTitle("INGROWN NAILS | WOUND CARE")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "1 INGROWN NAIL",
                            "2 INGROWN NAILS",
                            "3 INGROWN NAILS",
                            "4 INGROWN NAILS",
                            "6 INGROWN NAILS",
                            "WOUND CARE | LEVEL 1",
                            "WOUND CARE | LEVEL 2",
                            "WOUND CARE | LEVEL 3",
                            "WOUND CARE | LEVEL 4"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("grooming_service_performed")
                    .setTitle("GROOMING SERVICE PERFORMED")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Bath, Dry, Brushout",
                            "Included Services | Nail Trim, Oral Flush and Teeth Brushing, Ear Wiping and Cleaning, Eye Wiping, and Anal Gland Expression",
                            "Nail Trim",
                            "Sanitary Trim",
                            "Belly Shave",
                            "Pantaloon Trim",
                            "De-Shedding",
                            "Lion Cut",
                            "Squirrel Haircut",
                            "Mohawk Hairdo",
                            "Bison Haircut",
                            "Dino Haircut",
                            "Summer Shave",
                            "Teddy Bear Cut",
                            "Velvet Cut",
                            "Kept full tail",
                            "Big mane",
                            "Matting / Hair Compaction | Level 2",
                            "Matting / Hair Compaction | Level 3",
                            "Matting / Hair Compaction | Level 4",
                            "Matting / Hair Compaction | Felted Level 1",
                            "Matting / Hair Compaction | Felted Level 2",
                            "Matting Legs and Feet",
                            "Taper / Fade Mane",
                            "Trim Paw Pad Fur",
                            "Ear Flush",
                            "Eye and Nasal Cleaning",
                            "Ear Cleaning/Wiping",
                            "Oral Flush",
                            "Degreasing Hair Treatment",
                            "Dandruff Hair Treatment",
                            "De-shed Shampoo",
                            "Oatmeal Bath",
                            "Skin soothing bath (Oatmeal + Aloe)",
                            "Hypoallergenic Shampoo",
                            "Anti Microbial, Bacterial, Fungal",
                            "Soft Paws Nail Coverings | Front Set",
                            "Soft Paws Nail Coverings | Front and Back Set",
                            "Crate Cleaning | Crate Sanitizing",
                            "Fecal Removal | Level 1",
                            "Fecal Removal | Level 2",
                            "Impacted Anal Glands | Anal Gland Expression",
                            "Anal Gland Expression",
                            "Flea Bath Treatment",
                            "Cheristin Flea Treatment",
                            "Capstar Flea Treatment | For Fleas on the Face",
                            "Crate Cleaning | Crate Sanitizing",
                            "Paw Pad Moisturizer"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("collars_please_leave_collars_at_home")
                    .setTitle("Collars | Please leave collars at home")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "The collar is inside the carrier.",
                            "The collar is inside the pocket of the carrier.",
                            "We attached the collar to the carrier.",
                            "Kitty is wearing the collar. We took it off for grooming and put it at completion."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("tandem_cat_grooming_team")
                    .setTitle("TANDEM Cat® Grooming Team")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "Shawn Lioyryan, CTCG, MSW",
                            "Rebecca, Grooming Assistant & Customer Liaison",
                            "Sam, TANDEM Cat® - Certified",
                            "Daniel Lioyryan, CTCG, Ph.D.",
                            "Chris, Lead Grooming Assistant",
                            "Phil, Lead Grooming Assistant",
                            "Liz, Lead Groomer",
                            "Mary, Lead Grooming Assistant",
                            "Naomi, Grooming Assistant",
                            "Tora, Lead Grooming Assistant",
                            "Marguerite, Lead Grooming Assistant",
                            "Quinn, Grooming Assistant & Customer Liaison"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("cost_cap")
                    .setTitle("Cost Cap")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "The cost of the grooming service is capped when you maintain the optimized grooming schedule prescribed by the grooming team. The grooming Cost Cap will increase to $349 if service is scheduled and performed after the Cost Cap Expiration date entered above.",
                                    "*Additional Fees Apply for SoftPaws, flea medications, skunk treatment, and shuttle service.",
                                    "Due to recurring flea presence, a price cap of $459 will now apply to any future services if your cat returns with active fleas four or more times."))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("hydration_levels")
                    .setTitle("Hydration Levels")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "Kitty has indicators of low hydration levels (dehydration)",
                                    "Tip 1: Make Sure Your Cat Always has a Fresh, Clean Bowl of Water",
                                    "Tip 2: Be Creative When Offering Water",
                                    "Tip 3: Use Pet Water Fountains",
                                    "Tip 4: Broth Can Be Your Cat’s Best Friend",
                                    "Tip 5: Provide Canned Cat Food Every Day",
                                    "Left untreated, dehydration can cause serious secondary health issues. If you are concerned at all that your cat may be dehydrated, the best first step is to call your veterinarian immediately!"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("urinary_health_observations_during_grooming")
                    .setTitle("Urinary Health | Observation(s) During Grooming")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(List.of(
                            "We observed Hematuria (blood in the urine)",
                            "We observed Urine that is cloudy and/or smells foul"))
                    .addAllChoices(List.of())
                    .build(),
            QuestionDef.newBuilder()
                    .setCategory(QuestionCategoryType.CUSTOMIZE_FEEDBACK)
                    .setType(QuestionType.MULTI_CHOICE)
                    .setKey("product_recommendations")
                    .setTitle("Product Recommendations")
                    .setRequired(false)
                    .setShow(true)
                    .addAllOptions(
                            List.of(
                                    "Davis Benzoyl Peroxide Dog & Cat Shampoo | For at home general shampoo we recommend",
                                    "Virbac Knockout E.S. Area Treatment Carpet Spray | Recommended for treating at home spaces"))
                    .addAllChoices(List.of())
                    .build());
}
