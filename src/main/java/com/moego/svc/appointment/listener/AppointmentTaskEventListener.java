package com.moego.svc.appointment.listener;

import com.moego.common.utils.CommonUtil;
import com.moego.svc.appointment.domain.AppointmentTask;
import com.moego.svc.appointment.listener.event.AppointmentTaskEvent;
import com.moego.svc.appointment.service.remote.NotificationRemoteService;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/2/17
 */
@Component
@RequiredArgsConstructor
public class AppointmentTaskEventListener {

    private final NotificationRemoteService notificationRemoteService;

    @EventListener
    public void onApplicationEvent(AppointmentTaskEvent.Updated event) {
        var before = event.getBefore();
        var after = event.getAfter();
        if (hasAssignedNewStaff(before, after)) {
            notificationRemoteService.notificationTaskAssignedToSpecificStaff(after, 1);
        }
    }

    private static boolean hasAssignedNewStaff(AppointmentTask before, AppointmentTask after) {
        return CommonUtil.isNormal(after.getStaffId()) && !Objects.equals(before.getStaffId(), after.getStaffId());
    }

    @EventListener
    public void onApplicationEvent(AppointmentTaskEvent.BatchUpdated event) {
        var idToTask = event.getBefore().getTasks().stream()
                .collect(Collectors.toMap(AppointmentTask::getId, Function.identity()));
        // 按 staff id 分组发送通知
        event.getAfter().getTasks().stream()
                .collect(Collectors.groupingBy(AppointmentTask::getStaffId))
                .forEach((staffId, tasks) -> tasks.stream()
                        .min(Comparator.comparing(AppointmentTask::getStartDate))
                        .ifPresent(latestTask -> {
                            int assignedCount = getAssignedCount(tasks, idToTask);
                            if (assignedCount == 0) {
                                return;
                            }
                            notificationRemoteService.notificationTaskAssignedToSpecificStaff(
                                    latestTask, assignedCount);
                        }));
    }

    static int getAssignedCount(List<AppointmentTask> tasks, Map<Long, AppointmentTask> idToBeforeTask) {
        return tasks.stream()
                .map(afterTask -> {
                    var beforeTask = idToBeforeTask.get(afterTask.getId());
                    if (beforeTask == null) {
                        return 0;
                    }
                    return hasAssignedNewStaff(beforeTask, afterTask) ? 1 : 0;
                })
                .reduce(Integer::sum)
                .orElse(0);
    }
}
