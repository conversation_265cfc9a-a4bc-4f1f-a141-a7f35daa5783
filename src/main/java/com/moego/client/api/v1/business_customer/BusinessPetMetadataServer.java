package com.moego.client.api.v1.business_customer;

import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.idl.client.business_customer.v1.BusinessPetMetadataServiceGrpc;
import com.moego.idl.client.business_customer.v1.ListPetBreedsParams;
import com.moego.idl.client.business_customer.v1.ListPetBreedsResult;
import com.moego.idl.client.business_customer.v1.ListPetCoatTypesParams;
import com.moego.idl.client.business_customer.v1.ListPetCoatTypesResult;
import com.moego.idl.client.business_customer.v1.ListPetMetadataParams;
import com.moego.idl.client.business_customer.v1.ListPetMetadataResult;
import com.moego.idl.client.business_customer.v1.ListPetTypesParams;
import com.moego.idl.client.business_customer.v1.ListPetTypesResult;
import com.moego.idl.models.business_customer.v1.BusinessPetMetadataView;
import com.moego.idl.service.business_customer.v1.BusinessPetBreedServiceGrpc.BusinessPetBreedServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetCoatTypeServiceGrpc.BusinessPetCoatTypeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetMetadataServiceGrpc.BusinessPetMetadataServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetTypeServiceGrpc.BusinessPetTypeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.ListPetBreedRequest;
import com.moego.idl.service.business_customer.v1.ListPetCoatTypeRequest;
import com.moego.idl.service.business_customer.v1.ListPetMetadataRequest;
import com.moego.idl.service.business_customer.v1.ListPetMetadataResponse;
import com.moego.idl.service.business_customer.v1.ListPetTypeRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/4/16
 */
@GrpcService
@RequiredArgsConstructor
public class BusinessPetMetadataServer extends BusinessPetMetadataServiceGrpc.BusinessPetMetadataServiceImplBase {

    private final IGroomingOnlineBookingService onlineBookingService;
    private final BusinessPetMetadataServiceBlockingStub petMetadataService;
    private final BusinessPetMetadataConverter petMetadataConverter;

    private final CustomerService customerService;
    private final BusinessPetTypeServiceBlockingStub petTypeService;
    private final BusinessPetBreedServiceBlockingStub breedService;
    private final BusinessPetCoatTypeServiceBlockingStub coatTypeService;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void listPetMetadata(ListPetMetadataParams request, StreamObserver<ListPetMetadataResult> responseObserver) {
        OBBusinessDTO dto = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));
        ListPetMetadataRequest listPetMetadataRequest = ListPetMetadataRequest.newBuilder()
                .addAllMetadataNames(request.getMetadataNamesList())
                .setCompanyId(dto.getCompanyId())
                .build();
        ListPetMetadataResponse response = petMetadataService.listPetMetadata(listPetMetadataRequest);
        List<BusinessPetMetadataView> views = petMetadataConverter.modelToView(response.getMetadataList());

        responseObserver.onNext(
                ListPetMetadataResult.newBuilder().addAllMetadata(views).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listPetTypes(ListPetTypesParams request, StreamObserver<ListPetTypesResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        var response = petTypeService.listPetType(ListPetTypeRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .build());

        responseObserver.onNext(ListPetTypesResult.newBuilder()
                .addAllTypes(response.getTypesList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listPetBreeds(ListPetBreedsParams request, StreamObserver<ListPetBreedsResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        var response = breedService.listPetBreed(ListPetBreedRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .setPetTypeId(request.getPetType())
                .build());

        responseObserver.onNext(ListPetBreedsResult.newBuilder()
                .addAllBreeds(response.getBreedsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listPetCoatTypes(
            ListPetCoatTypesParams request, StreamObserver<ListPetCoatTypesResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        var response = coatTypeService.listPetCoatType(ListPetCoatTypeRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .build());

        responseObserver.onNext(ListPetCoatTypesResult.newBuilder()
                .addAllCoatTypes(response.getCoatTypesList())
                .build());
        responseObserver.onCompleted();
    }
}
