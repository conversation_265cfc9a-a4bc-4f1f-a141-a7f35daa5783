package com.moego.client.api.v1.business_customer;

import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.offering.v1.EvaluationView;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/1/18
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BusinessPetEvaluationConverter {
    BusinessPetEvaluationConverter INSTANCE = Mappers.getMapper(BusinessPetEvaluationConverter.class);

    List<EvaluationView> convertModelToView(List<EvaluationModel> evaluationViews);
}
