package com.moego.client.api.v1.online_booking.converter;

import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitView;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface LodgingUnitConverter {
    LodgingUnitConverter INSTANCE = Mappers.getMapper(LodgingUnitConverter.class);

    LodgingUnitView toLodgingUnitView(LodgingUnitModel model);
}
