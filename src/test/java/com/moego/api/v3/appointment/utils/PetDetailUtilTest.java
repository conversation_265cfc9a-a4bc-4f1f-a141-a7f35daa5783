package com.moego.api.v3.appointment.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class PetDetailUtilTest {

    @Test
    void getPetEvaluationIDs_returnsDistinctServiceIds() {
        var evaluation1 = EvaluationServiceModel.newBuilder()
                .setPetId(1L)
                .setServiceId(1L)
                .build();
        var evaluation2 = EvaluationServiceModel.newBuilder()
                .setPetId(2L)
                .setServiceId(2L)
                .build();
        var evaluation3 = EvaluationServiceModel.newBuilder()
                .setPetId(3L)
                .setServiceId(1L)
                .build();

        var result = PetDetailUtil.getPetEvaluationIDs(List.of(evaluation1, evaluation2, evaluation3));

        assertThat(result).isEqualTo(List.of(1L, 2L));
    }

    @Test
    void getPetEvaluationIDs_handlesEmptyList() {
        List<Long> result = PetDetailUtil.getPetEvaluationIDs(List.of());

        assertThat(result).isEmpty();
    }

    @Test
    void getPetDetailLodgingIDs_returnsDistinctLodgingIds() {
        var petDetail1 = PetDetailModel.newBuilder().setLodgingId(1L).build();
        var petDetail2 = PetDetailModel.newBuilder().setLodgingId(2L).build();
        var evaluation1 = EvaluationServiceModel.newBuilder().setLodgingId(1L).build();
        var evaluation2 = EvaluationServiceModel.newBuilder().setLodgingId(3L).build();
        var evaluation3 = EvaluationServiceModel.newBuilder().build();

        var result = PetDetailUtil.getPetDetailLodgingIDs(
                List.of(petDetail1, petDetail2), List.of(evaluation1, evaluation2, evaluation3));

        assertThat(result).isEqualTo(List.of(1L, 2L, 3L));
    }

    @Test
    void getPetDetailLodgingIDs_handlesEmptyLists() {
        var result = PetDetailUtil.getPetDetailLodgingIDs(List.of(), List.of());

        assertThat(result).isEmpty();
    }

    @Test
    void getStaffIDs_returnsDistinctStaffIds() {
        var petDetail1 = PetDetailModel.newBuilder().setStaffId(1L).build();
        var petDetail2 = PetDetailModel.newBuilder().setStaffId(2L).build();
        var evaluation1 = EvaluationServiceModel.newBuilder().setStaffId(1L).build();
        var evaluation2 = EvaluationServiceModel.newBuilder().setStaffId(3L).build();
        var evaluation3 = EvaluationServiceModel.newBuilder().build();

        var result = PetDetailUtil.getStaffIDs(
                List.of(petDetail1, petDetail2), List.of(evaluation1, evaluation2, evaluation3));

        assertThat(result).isEqualTo(List.of(1L, 2L, 3L));
    }

    @Test
    void getStaffIDs_handlesEmptyLists() {
        var result = PetDetailUtil.getStaffIDs(List.of(), List.of());

        assertThat(result).isEmpty();
    }

    @Test
    void getPetAppointments_returnsCorrectMapping() {
        var petDetail11 =
                PetDetailModel.newBuilder().setPetId(1L).setGroomingId(101L).build();
        var petDetail12 =
                PetDetailModel.newBuilder().setPetId(1L).setGroomingId(101L).build();
        var petDetail2 =
                PetDetailModel.newBuilder().setPetId(2L).setGroomingId(102L).build();
        var evaluation1 = EvaluationServiceModel.newBuilder()
                .setPetId(1L)
                .setAppointmentId(201L)
                .build();
        var evaluation2 = EvaluationServiceModel.newBuilder()
                .setPetId(3L)
                .setAppointmentId(202L)
                .build();

        var result = PetDetailUtil.getPetAppointments(
                List.of(petDetail11, petDetail12, petDetail2), List.of(evaluation1, evaluation2));

        assertThat(result).isEqualTo(Map.of(1L, List.of(101L, 201L), 2L, List.of(102L), 3L, List.of(202L)));
    }

    @Test
    void getPetAppointments_handlesEmptyLists() {
        var result = PetDetailUtil.getPetAppointments(List.of(), List.of());
        assertThat(result).isEmpty();
    }

    @Test
    void getPetAppointments_handlesNullLists() {
        var result = PetDetailUtil.getPetAppointments(null, null);
        assertThat(result).isEmpty();
    }

    @Test
    void isContainDate_returnsTrueForSpecificDate() {
        var result = PetDetailUtil.isContainDate("2023-10-10", null, null, "[\"2023-10-10\"]");
        assertThat(result).isTrue();
    }

    @Test
    void isContainDate_returnsFalseForSpecificDate() {
        var result = PetDetailUtil.isContainDate("2023-10-11", null, null, "[\"2023-10-10\"]");
        assertThat(result).isFalse();
    }

    @Test
    void isContainDate_returnsTrueForDateRange() {
        var result = PetDetailUtil.isContainDate("2023-10-10", "2023-10-01", "2023-10-15", null);
        assertThat(result).isTrue();
    }

    @Test
    void isContainDate_returnsTrueForDateRange2() {
        var result = PetDetailUtil.isContainDate("2023-10-01", "2023-10-01", "2023-10-15", null);
        assertThat(result).isTrue();
    }

    @Test
    void isContainDate_returnsTrueForDateRange3() {
        var result = PetDetailUtil.isContainDate("2023-10-15", "2023-10-01", "2023-10-15", null);
        assertThat(result).isTrue();
    }

    @Test
    void isContainDate_returnsFalseForDateRange() {
        var result = PetDetailUtil.isContainDate("2023-10-16", "2023-10-01", "2023-10-15", null);
        assertThat(result).isFalse();
    }

    @Test
    void getSpecificDates_returnsParsedDates() {
        var result = PetDetailUtil.getSpecificDates("[\"2023-10-10\", \"2023-10-11\"]");
        assertThat(result).isEqualTo(List.of("2023-10-10", "2023-10-11"));
    }

    @Test
    void getSpecificDates_handlesEmptyString() {
        var result = PetDetailUtil.getSpecificDates("");
        assertThat(result).isEmpty();
    }
}
