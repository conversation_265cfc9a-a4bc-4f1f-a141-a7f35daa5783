package com.moego.server.grooming.service;

import com.google.rpc.Code;
import com.google.type.LatLng;
import com.moego.common.constant.CommonConstant;
import com.moego.idl.models.map.v1.AddressModel;
import com.moego.idl.models.map.v1.PolylineEncoding;
import com.moego.idl.models.map.v1.RouteMatrixElement;
import com.moego.idl.models.map.v1.RoutePreference;
import com.moego.idl.models.map.v1.TravelMode;
import com.moego.idl.models.map.v1.Units;
import com.moego.idl.models.map.v1.Waypoint;
import com.moego.idl.service.map.v1.GetAddressRequest;
import com.moego.idl.service.map.v1.MapServiceGrpc;
import com.moego.idl.service.map.v1.QueryRouteByPointRequest;
import com.moego.idl.service.map.v1.QueryRouteMatrixByPointsRequest;
import com.moego.idl.service.map.v1.QueryRoutesRequest;
import com.moego.idl.service.map.v1.QueryRoutesResponse;
import com.moego.idl.service.map.v1.RoutesServiceGrpc;
import com.moego.lib.utils.CoreUtils;
import com.moego.server.grooming.service.dto.RouteResult;
import com.moego.server.grooming.service.utils.SmartScheduleUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@Configuration
public class GoogleMapService {

    @Value("${default.max.driving.speed}")
    private double defaultMaxDrivingSpeed;

    @Autowired
    private MapServiceGrpc.MapServiceBlockingStub mapClient;

    @Autowired
    private RoutesServiceGrpc.RoutesServiceBlockingStub routesClient;

    /**
     * 根据坐标查询距离指标，先从缓存中取，如果缓存中没有，则调用 google maps api 查询
     *
     * @param from
     * @param to
     * @return
     */
    public RouteMatrixElement queryMatrix(LatLng from, LatLng to) {
        var response = queryMatrix(Collections.singletonList(from), Collections.singletonList(to));
        if (response.isEmpty()) {
            return null;
        }
        return response.get(0);
    }

    public List<RouteMatrixElement> queryMatrix(List<LatLng> origins, List<LatLng> destinations) {
        var request = QueryRouteMatrixByPointsRequest.newBuilder()
                .addAllOrigins(origins)
                .addAllDestinations(destinations)
                .build();
        var response = routesClient.queryRouteMatrixByPoints(request);
        return response.getRowsList();
    }

    public static boolean isValidMatrixElement(RouteMatrixElement element) {
        return element != null
                && element.hasStatus()
                && Code.OK.getNumber() == element.getStatus().getCode();
    }

    /**
     * 根据多个出发地点查询到一个目的地点的驾驶距离指标
     * @param locationsFrom <uniqueKey, location>
     * @param locationTo 目的地点
     * @return <uniqueKey, RouteMatrixElement>
     */
    public Map<Long, RouteMatrixElement> queryDrivingFrom(Map<Long, LatLng> locationsFrom, LatLng locationTo) {
        Map<Long, RouteMatrixElement> result = new HashMap<>();
        if (CollectionUtils.isEmpty(locationsFrom)) {
            return result;
        }

        List<Long> ids = locationsFrom.keySet().stream().toList();
        List<LatLng> locations = ids.stream().map(locationsFrom::get).toList();
        var fromElements = queryMatrix(locations, List.of(locationTo));
        for (var element : fromElements) {
            if (!GoogleMapService.isValidMatrixElement(element)) {
                continue;
            }
            Long id = ids.get(element.getOriginIndex());
            result.put(id, element);
        }
        return result;
    }

    /**
     * 根据一个出发地点查询到多个目的地点的驾驶距离指标
     * @param locationFrom 出发地点
     * @param locationsTo <uniqueKey, location>
     * @return <uniqueKey, RouteMatrixElement>
     */
    public Map<Long, RouteMatrixElement> queryDrivingTo(LatLng locationFrom, Map<Long, LatLng> locationsTo) {
        Map<Long, RouteMatrixElement> result = new HashMap<>();
        if (CollectionUtils.isEmpty(locationsTo)) {
            return result;
        }

        List<Long> ids = locationsTo.keySet().stream().toList();
        List<LatLng> locations = ids.stream().map(locationsTo::get).toList();
        var toElements = queryMatrix(List.of(locationFrom), locations);
        for (var element : toElements) {
            if (!GoogleMapService.isValidMatrixElement(element)) {
                continue;
            }
            Long id = ids.get(element.getDestinationIndex());
            result.put(id, element);
        }
        return result;
    }

    public static boolean isValidRoutes(QueryRoutesResponse response) {
        return response != null
                && response.hasStatus()
                && Code.OK.getNumber() == response.getStatus().getCode()
                && 0 < response.getRoutesCount();
    }

    public static double metersToMiles1ScaleFloor(double meters) {
        return BigDecimal.valueOf(meters / CommonConstant.METERS_PER_MILE)
                .setScale(1, RoundingMode.FLOOR)
                .doubleValue();
    }

    public static int metersToMiles(double meters) {
        return (int) (meters / CommonConstant.METERS_PER_MILE);
    }

    public static int secondsToMinutes(long seconds) {
        return (int) (seconds / 60);
    }

    public static double takeMilesFromElement1ScaleFloor(RouteMatrixElement element) {
        return metersToMiles1ScaleFloor(element.getDistance());
    }

    public static int takeMinutesFromElement(RouteMatrixElement element) {
        return secondsToMinutes(element.getDuration().getSeconds());
    }

    // 根据距离(单位：米)、速度(单位：m/s)计算行驶的时间(单位：分钟)
    private static int calcMinutes(double meters, double speed) {
        return (int) ((meters / speed) / 60);
    }

    // 计算 slot 中的可用时间
    public void calculateAvailableTime(
            List<TimeSlot> slots, String lat, String lng, Integer serviceDuration, Integer bufferTime) {
        // boolean[] beforeCalled = new boolean[slots.size()];
        // boolean[] afterCalled = new boolean[slots.size()];
        // Coordinate targetPoint = new Coordinate(lat, lng);
        var targetPoint = toGoogleLatLng(lat, lng);
        var slotDurationMap = new HashMap<Integer, Integer>();
        var defaultDuration = serviceDuration + bufferTime;
        for (int i = 0; i < slots.size(); ++i) {
            TimeSlot ts = slots.get(i);
            var totalDuration = SmartScheduleUtil.getTotalDuration(ts, serviceDuration, bufferTime);
            slotDurationMap.put(i, totalDuration);
            ts.updateAvailableTime(totalDuration);
            // beforeCalled[i] = false;
            // afterCalled[i] = false;
            // 先根据至少预留的 duration 时间判定是否符合，如果符合，则按直线距离评估最短时间再进行判定。
            if (0 < ts.getAvailableTime()) {
                int inMinutes = 0;
                double inMiles = 0;
                int outMinutes = 0;
                double outMiles = 0;
                if (ts.isBeforeAddressValid()) {
                    var point = toGoogleLatLng(ts.getBeforeLat(), ts.getBeforeLng());
                    double beforeDistance = straightDistanceByAngle(point, targetPoint);
                    // Utils.straightDistance(new Coordinate(ts.getBeforeLat(), ts.getBeforeLng()), targetPoint);
                    inMinutes = calcMinutes(beforeDistance, defaultMaxDrivingSpeed);
                    inMiles = metersToMiles1ScaleFloor(beforeDistance);
                }
                if (ts.isAfterAddressValid()) {
                    var point = toGoogleLatLng(ts.getAfterLat(), ts.getAfterLng());
                    double afterDistance = straightDistanceByAngle(point, targetPoint);
                    // Utils.straightDistance(new Coordinate(ts.getAfterLat(), ts.getAfterLng()), targetPoint);
                    outMinutes = calcMinutes(afterDistance, defaultMaxDrivingSpeed);
                    outMiles = metersToMiles1ScaleFloor(afterDistance);
                }
                ts.setDriveInMinutes(inMinutes);
                ts.setDriveInMiles(inMiles);
                ts.setDriveOutMinutes(outMinutes);
                ts.setDriveOutMiles(outMiles);
                ts.updateAvailableTime(totalDuration + ts.getDriveInMinutes() + ts.getDriveOutMinutes());
            }
        }

        // 收集需要调用 google maps API 计算 inMinutes 的 slot
        List<Integer> beforeList = new ArrayList<>();
        for (int i = 0; i < slots.size(); ++i) {
            TimeSlot ts = slots.get(i);
            // 有空余时间 && 前面的地址有效 && 没有调用 API 计算过 inMinutes
            if (0 <= ts.getAvailableTime() && ts.isBeforeAddressValid() /*&& !beforeCalled[i]*/) {
                beforeList.add(i);
            }
        }

        if (!beforeList.isEmpty()) {
            List<com.google.type.LatLng> points = new ArrayList<>();
            // Coordinate[] cs = new Coordinate[beforeList.size()];
            for (var index : beforeList) {
                TimeSlot ts = slots.get(index);
                points.add(toGoogleLatLng(ts.getBeforeLat(), ts.getBeforeLng()));
                // cs[i] = new Coordinate(ts.getBeforeLat(), ts.getBeforeLng());
            }

            var elements = queryMatrix(points, Collections.singletonList(targetPoint));
            for (var element : elements) {
                var index = beforeList.get(element.getOriginIndex());
                TimeSlot ts = slots.get(index);
                if (isValidMatrixElement(element)) {
                    ts.setDriveInMinutes(takeMinutesFromElement(element));
                    ts.setDriveInMiles(takeMilesFromElement1ScaleFloor(element));
                    ts.updateAvailableTime(slotDurationMap.getOrDefault(index, defaultDuration)
                            + ts.getDriveInMinutes()
                            + ts.getDriveOutMinutes());
                } else {
                    ts.setDriveInMinutes(0);
                    ts.setDriveInMiles(0);
                    log.info(
                            "warning: query google api failed, from {} to {}",
                            points.get(element.getOriginIndex()),
                            targetPoint);
                }
            }
        }

        // 收集需要调用 google maps API 计算 outMinutes 的 slot
        List<Integer> afterList = new ArrayList<>();
        for (int i = 0; i < slots.size(); ++i) {
            TimeSlot ts = slots.get(i);
            // 有空余时间 && 后面的地址有效 && 没有调用 API 计算过 outMinutes
            if (0 <= ts.getAvailableTime() && ts.isAfterAddressValid() /*&& !afterCalled[i]*/) {
                afterList.add(i);
            }
        }

        if (!afterList.isEmpty()) {
            List<com.google.type.LatLng> points = new ArrayList<>();
            // Coordinate[] cs = new Coordinate[afterList.size()];
            for (var index : afterList) {
                TimeSlot ts = slots.get(index);
                points.add(toGoogleLatLng(ts.getAfterLat(), ts.getAfterLng()));
                // cs[i] = new Coordinate(ts.getAfterLat(), ts.getAfterLng());
            }

            var elements = queryMatrix(Collections.singletonList(targetPoint), points);
            for (var element : elements) {
                var index = afterList.get(element.getDestinationIndex());
                TimeSlot ts = slots.get(index);
                if (isValidMatrixElement(element)) {
                    ts.setDriveOutMinutes(takeMinutesFromElement(element));
                    ts.setDriveOutMiles(takeMilesFromElement1ScaleFloor(element));
                    ts.updateAvailableTime(slotDurationMap.getOrDefault(index, defaultDuration)
                            + ts.getDriveInMinutes()
                            + ts.getDriveOutMinutes());
                } else {
                    ts.setDriveOutMinutes(0);
                    ts.setDriveOutMiles(0);
                    log.info(
                            "warning: query google api failed, from {} to {}",
                            targetPoint,
                            points.get(element.getDestinationIndex()));
                }
            }
        }

        log.info("after fill in google map driving time: {}", slots);
    }

    // 计算 slot 中的可用时间 ，按直线距离评估最短时间
    public void calculateAvailableTimeStraightDistance(List<TimeSlot> slots, String lat, String lng, Integer duration) {
        var targetPoint = toGoogleLatLng(lat, lng);

        for (TimeSlot ts : slots) {
            ts.updateAvailableTime(duration);
            // 先根据至少预留的 duration 时间判定是否符合，如果符合，则
            if (ts.getAvailableTime() <= 0) {
                continue;
            }
            int inMinutes = 0;
            double inMiles = 0;
            int outMinutes = 0;
            double outMiles = 0;
            if (ts.isBeforeAddressValid()) {
                var point = toGoogleLatLng(ts.getBeforeLat(), ts.getBeforeLng());
                double beforeDistance = straightDistanceByAngle(point, targetPoint);
                inMinutes = calcMinutes(beforeDistance, defaultMaxDrivingSpeed);
                inMiles = metersToMiles1ScaleFloor(beforeDistance);
            }
            if (ts.isAfterAddressValid()) {
                var point = toGoogleLatLng(ts.getAfterLat(), ts.getAfterLng());
                double afterDistance = straightDistanceByAngle(point, targetPoint);
                outMinutes = calcMinutes(afterDistance, defaultMaxDrivingSpeed);
                outMiles = metersToMiles1ScaleFloor(afterDistance);
            }
            ts.setDriveInMinutes(inMinutes);
            ts.setDriveInMiles(inMiles);
            ts.setDriveOutMinutes(outMinutes);
            ts.setDriveOutMiles(outMiles);
            ts.updateAvailableTime(duration + ts.getDriveInMinutes() + ts.getDriveOutMinutes());
        }
    }

    // 根据坐标查 zipcode
    public AddressModel queryAddress(double lat, double lng) {
        try {
            var point = toGoogleLatLng(lat, lng);
            var request = GetAddressRequest.newBuilder().setCoordinate(point).build();
            var response = mapClient.getAddress(request);
            if (response.hasAddress()) {
                return response.getAddress();
            }

            return null;
        } catch (Exception e) {
            log.error("Failed querying zipcode from google: {}", e.getMessage(), e);
            return null;
        }
    }

    // 调用 Google API 查询路线数据，同时缓存到 redis
    public RouteResult queryAndCacheRoutes(
            LatLng origin, LatLng destination, List<LatLng> waypoints, boolean needOptimize) {
        com.moego.idl.models.map.v1.Route route = null;
        if (waypoints == null || waypoints.isEmpty()) {
            var request = QueryRouteByPointRequest.newBuilder()
                    .setOrigin(origin)
                    .setDestination(destination)
                    .build();
            var response = routesClient.queryRouteByPoint(request);
            if (isValidRoutes(response)) {
                route = response.getRoutes(0);
            }
        } else {
            var builder = QueryRoutesRequest.newBuilder();
            builder.addWaypoints(toWaypoint(origin));
            for (var point : waypoints) {
                builder.addWaypoints(toWaypoint(point));
            }
            builder.addWaypoints(toWaypoint(destination));
            builder.setTravelMode(TravelMode.DRIVING);
            builder.setRoutePreference(RoutePreference.TRAFFIC_AWARE);
            builder.setPolylineEncoding(PolylineEncoding.ENCODED_POLYLINE);
            builder.setUnits(Units.IMPERIAL);
            builder.addFieldMask("routes.polyline");
            builder.setOptimizeWaypointOrder(needOptimize);
            var response = routesClient.queryRoutes(builder.build());
            if (isValidRoutes(response)) {
                route = response.getRoutes(0);
            }
        }

        if (route == null) {
            log.warn("call google maps routes api from {} to {} failed !", origin, destination);
            return null;
        }

        BigDecimal drivingMiles = BigDecimal.valueOf(metersToMiles1ScaleFloor(route.getDistance()));
        Integer drivingMinutes = secondsToMinutes(route.getDuration().getSeconds());
        List<Integer> waypointIndexList = new ArrayList<>();
        if (0 < route.getOptimizedPointIndexCount()) {
            for (var index : route.getOptimizedPointIndexList()) {
                waypointIndexList.add(index - 1);
            }
        }
        String polyline = route.getPolyline().getEncodedPolyline();
        List<Integer> drivingMinuteList = route.getLegsList().stream()
                .map(leg -> secondsToMinutes(leg.getDuration().getSeconds()))
                .toList();
        List<BigDecimal> drivingMileList = route.getLegsList().stream()
                .map(leg -> BigDecimal.valueOf(metersToMiles1ScaleFloor(leg.getDistance())))
                .toList();

        return new RouteResult(
                waypointIndexList, polyline, drivingMiles, drivingMinutes, drivingMinuteList, drivingMileList);
    }

    static double straightDistanceByAngle(LatLng from, LatLng to) {
        return CoreUtils.straightDistanceByAngle(
                from.getLatitude(), from.getLongitude(), to.getLatitude(), to.getLongitude());
    }

    public static com.google.type.LatLng toGoogleLatLng(String lat, String lng) {
        return toGoogleLatLng(Double.parseDouble(lat), Double.parseDouble(lng));
    }

    static com.google.type.LatLng toGoogleLatLng(double lat, double lng) {
        return com.google.type.LatLng.newBuilder()
                .setLatitude(lat)
                .setLongitude(lng)
                .build();
    }

    static Waypoint toWaypoint(LatLng point) {
        return Waypoint.newBuilder().setCoordinate(point).build();
    }
}
