package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingRepeat;
import com.moego.server.grooming.mapperbean.MoeGroomingRepeatExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingRepeatMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingRepeatExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingRepeatExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    int insert(MoeGroomingRepeat record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingRepeat record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    List<MoeGroomingRepeat> selectByExample(MoeGroomingRepeatExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    MoeGroomingRepeat selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingRepeat record, @Param("example") MoeGroomingRepeatExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeGroomingRepeat record, @Param("example") MoeGroomingRepeatExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingRepeat record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingRepeat record);

    List<MoeGroomingRepeat> queryByBusinessId(@Param("businessId") Integer businessId);
}
