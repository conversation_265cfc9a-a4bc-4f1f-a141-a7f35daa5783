package com.moego.server.grooming.service;

import com.moego.idl.models.appointment.v1.OutboxSendStatus;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.server.grooming.convert.OutboxConverter;
import com.moego.server.grooming.mapper.AppointmentOutboxMapper;
import com.moego.server.grooming.mapperbean.AppointmentOutbox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 封装对 grooming note 的操作
 *
 * <AUTHOR>
 * @since 2022/5/12 16:15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppointmentOutboxService {

    private final AppointmentOutboxMapper appointmentOutboxMapper;

    /**
     * 记录待推送消息
     */
    public Long create(String topicName, EventRecord<EventData> eventRecord) {
        var po = OutboxConverter.INSTANCE.toAppointmentOutbox(topicName, eventRecord);
        appointmentOutboxMapper.insertSelective(po);
        return po.getId();
    }

    public void updateStatus(Long id, OutboxSendStatus status) {
        var po = new AppointmentOutbox();
        po.setId(id);
        po.setStatus(status);
        appointmentOutboxMapper.updateByPrimaryKeySelective(po);
    }
}
