package com.moego.server.grooming.web.vo.client;

import com.moego.server.grooming.dto.AutoAssignDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/14
 */
@Data
@Accessors(chain = true)
public class ApptDetailVO {

    @Schema(description = "Ticket unique identifier")
    private String bookingId;

    @Schema(description = "Business id")
    private Integer businessId;

    @Schema(description = "Customer id")
    private Integer customerId;

    @Schema(description = "0-Requests, 1-Upcoming, 2-Completed")
    private Byte apptType;

    @Schema(description = "Appointment date, yyyy-MM-dd")
    private String apptDate;

    @Schema(description = "Appointment start time, minute offset of the day")
    private Integer apptStartTime;

    private Boolean noStartTime;

    @Schema(description = "Appointment end time, minute offset of the day")
    private Integer apptEndTime;

    @Schema(description = "1-unconfirmed, 2-confirmed, 3-finished, 10-submitted, 11-accepted")
    private Byte status;

    @Schema(description = "2-unpaid, 3-partial paid, 1-fully paid")
    private Byte paymentStatus;

    @Schema(description = "Source, 22168-ob, 22018-web, 17216-android, 17802-ios")
    private Integer source;

    private AutoAssignDTO autoAssign;
}
