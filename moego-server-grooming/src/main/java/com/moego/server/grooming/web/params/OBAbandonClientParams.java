package com.moego.server.grooming.web.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public record OBAbandonClientParams(
        @Schema(description = "existing client id") @Min(0) Integer customerId,
        @Schema(description = "referer link") @Size(max = 1024) String referer,
        @Schema(description = "abandon client unique phone number") @Size(max = 50) String phoneNumber,
        @Schema(description = "abandon client first name") @Size(max = 50) String firstName,
        @Schema(description = "abandon client last name") @Size(max = 50) String lastName,
        @Schema(description = "abandon client unique email") @Size(max = 50) String email,
        @Schema(description = "custom question answers, key:custom_questionID, value: answer")
                Map<String, String> answersMap,
        @Schema(description = "existing address id") @Min(0) Integer addressId,
        @Schema(description = "address line1") @Size(max = 255) String address1,
        @Schema(description = "address line2") @Size(max = 255) String address2,
        @Schema(description = "city") @Size(max = 255) String city,
        @Schema(description = "state") @Size(max = 255) String state,
        @Schema(description = "country") @Size(max = 255) String country,
        @Schema(description = "zipcode") @Size(max = 10) String zipcode,
        @Schema(description = "latitude") @Size(max = 50) String lat,
        @Schema(description = "longitude") @Size(max = 50) String lng,
        boolean isProfileRequestAddress) {}
