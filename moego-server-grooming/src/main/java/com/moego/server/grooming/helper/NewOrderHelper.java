package com.moego.server.grooming.helper;

import com.moego.idl.models.appointment.v1.AppointmentExtraInfoModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.CreateExtraInfoRequest;
import com.moego.idl.service.appointment.v1.ListExtraInfoRequest;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderRequest;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderResponse;
import com.moego.idl.service.order.v1.CreateDepositOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2025/5/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NewOrderHelper {

    private final FeatureFlagApi featureFlagApi;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderStub;
    private final AppointmentMapperProxy appointmentMapper;

    public void insertNewOrder(long appointmentId) {
        appointmentStub.createExtraInfo(CreateExtraInfoRequest.newBuilder()
                .setAppointmentId(appointmentId)
                .setIsNewOrder(true)
                .build());
    }

    public boolean isNewOrder(long appointmentId) {
        var extraInfos = appointmentStub
                .listExtraInfo(ListExtraInfoRequest.newBuilder()
                        .addAppointmentIds(appointmentId)
                        .build())
                .getExtraInfoList()
                .stream()
                .filter(e -> Objects.equals(e.getAppointmentId(), appointmentId))
                .toList();
        if (CollectionUtils.isEmpty(extraInfos)) {
            return false;
        }
        return extraInfos.stream().anyMatch(AppointmentExtraInfoModel::getIsNewOrder);
    }

    public Map<Long, Boolean> listNewOrder(List<Long> appointmentIds) {
        var appointmentIdToNewOrder = appointmentStub
                .listExtraInfo(ListExtraInfoRequest.newBuilder()
                        .addAllAppointmentIds(appointmentIds)
                        .build())
                .getExtraInfoList()
                .stream()
                .collect(Collectors.toMap(
                        AppointmentExtraInfoModel::getAppointmentId, AppointmentExtraInfoModel::getIsNewOrder));

        return appointmentIds.stream()
                .collect(Collectors.toMap(
                        appointmentId -> appointmentId,
                        appointmentId -> appointmentIdToNewOrder.getOrDefault(appointmentId, false)));
    }

    public boolean enableNewOrder(long companyId) {
        try {
            return featureFlagApi.isOn(
                    FeatureFlags.NEW_ORDER_FLOW,
                    FeatureFlagContext.builder().company(companyId).build());
        } catch (Exception e) {
            log.error("Failed to check feature flag for new order flow, companyId: {}", companyId, e);
            return false;
        }
    }

    public Long createPreAuthOrder(long appointmentId) {
        var appointment = appointmentMapper.selectByPrimaryKey((int) appointmentId);
        var estimatedOrder = appointmentStub
                .previewEstimateOrder(PreviewEstimateOrderRequest.newBuilder()
                        .setCompanyId(appointment.getCompanyId())
                        .setBusinessId(appointment.getBusinessId())
                        .addAppointmentIds(appointmentId)
                        .build())
                .getEstimatedOrdersList()
                .stream()
                .collect(Collectors.toMap(
                        PreviewEstimateOrderResponse.EstimatedOrder::getAppointmentId,
                        Function.identity(),
                        (a, b) -> a))
                .getOrDefault(appointmentId, PreviewEstimateOrderResponse.EstimatedOrder.getDefaultInstance());

        var createDepositRequest = CreateDepositOrderRequest.newBuilder()
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId())
                .setSourceType(OrderSourceType.APPOINTMENT)
                .setSourceId(appointment.getId())
                .setDepositAmount(estimatedOrder.getEstimatedTotal())
                .build();
        return orderStub
                .createDepositOrder(createDepositRequest)
                .getOrder()
                .getOrder()
                .getId();
    }
}
