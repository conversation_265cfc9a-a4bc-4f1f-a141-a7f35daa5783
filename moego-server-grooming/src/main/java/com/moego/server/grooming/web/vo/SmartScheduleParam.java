package com.moego.server.grooming.web.vo;

import com.moego.server.business.dto.ParsedCloseDate;
import com.moego.server.business.dto.SmartScheduleSettingDTO;
import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.params.ss.SmartScheduleRequest;
import com.moego.server.grooming.service.dto.ob.OBPetLimitFilterDTO;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/8/11 3:47 PM
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SmartScheduleParam extends SmartScheduleRequest {

    private LocalDate startDate;
    private Integer businessId;
    Map<Integer, StaffSmartScheduleSettingDTO> staffSsSettingMap;

    @Deprecated
    SmartScheduleSettingDTO smartScheduleInfo;

    List<ParsedCloseDate> allClosedDate;

    @Deprecated
    Map<Integer, Map<Integer, TimeRangeDto>> weekStaffWorkingHours;

    @Deprecated
    Map<Integer, Map<Integer, List<TimeRangeDto>>> obStaffWorkingHours;

    Map<Integer /* dayOfWeek */, Map<Integer /* staffId */, StaffTime>> obStaffTime;

    private Integer filterGroomingId;

    private Map<Integer, CustomerAddressDto> customerAddressDtoMap;

    /**
     * have time slot flag
     */
    private Map<String, Boolean> amTimeSlotMap = new ConcurrentHashMap<>();

    private Map<String, Boolean> pmTimeSlotMap = new ConcurrentHashMap<>();

    @Deprecated
    private Byte useVersion;

    private List<OBPetDataDTO> petParamList;

    private OBPetLimitFilterDTO obPetLimitFilterDTO;

    @Deprecated
    private Map<Integer, Map<LocalDate, List<TimeRangeDto>>> staffDateWorkingHours;

    private Map<String, Map<Integer, StaffTime>> staffTime;

    @Deprecated
    private Map<Integer, List<String>> staffOverrideDateList;
}
