package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_question_save
 */
public class MoeBookOnlineQuestionSave {
    /**
     * Database Column Remarks:
     *   商家客户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   客户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   宠物id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.pet_id
     *
     * @mbg.generated
     */
    private Integer petId;

    /**
     * Database Column Remarks:
     *   1 pet的json  2pet owner的json
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     * Database Column Remarks:
     *   1 正常  2update导致的弃用
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   custom question answers
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.question_json
     *
     * @mbg.generated
     */
    private String questionJson;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question_save.form_json
     *
     * @mbg.generated
     */
    private String formJson;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.id
     *
     * @return the value of moe_book_online_question_save.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.id
     *
     * @param id the value for moe_book_online_question_save.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.business_id
     *
     * @return the value of moe_book_online_question_save.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.business_id
     *
     * @param businessId the value for moe_book_online_question_save.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.customer_id
     *
     * @return the value of moe_book_online_question_save.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.customer_id
     *
     * @param customerId the value for moe_book_online_question_save.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.pet_id
     *
     * @return the value of moe_book_online_question_save.pet_id
     *
     * @mbg.generated
     */
    public Integer getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.pet_id
     *
     * @param petId the value for moe_book_online_question_save.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.type
     *
     * @return the value of moe_book_online_question_save.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.type
     *
     * @param type the value for moe_book_online_question_save.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.status
     *
     * @return the value of moe_book_online_question_save.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.status
     *
     * @param status the value for moe_book_online_question_save.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.create_time
     *
     * @return the value of moe_book_online_question_save.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.create_time
     *
     * @param createTime the value for moe_book_online_question_save.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.update_time
     *
     * @return the value of moe_book_online_question_save.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.update_time
     *
     * @param updateTime the value for moe_book_online_question_save.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.company_id
     *
     * @return the value of moe_book_online_question_save.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.company_id
     *
     * @param companyId the value for moe_book_online_question_save.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.question_json
     *
     * @return the value of moe_book_online_question_save.question_json
     *
     * @mbg.generated
     */
    public String getQuestionJson() {
        return questionJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.question_json
     *
     * @param questionJson the value for moe_book_online_question_save.question_json
     *
     * @mbg.generated
     */
    public void setQuestionJson(String questionJson) {
        this.questionJson = questionJson == null ? null : questionJson.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question_save.form_json
     *
     * @return the value of moe_book_online_question_save.form_json
     *
     * @mbg.generated
     */
    public String getFormJson() {
        return formJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question_save.form_json
     *
     * @param formJson the value for moe_book_online_question_save.form_json
     *
     * @mbg.generated
     */
    public void setFormJson(String formJson) {
        this.formJson = formJson == null ? null : formJson.trim();
    }
}
