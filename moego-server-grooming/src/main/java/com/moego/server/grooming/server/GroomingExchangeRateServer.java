package com.moego.server.grooming.server;

import com.moego.common.response.ResponseResult;
import com.moego.server.grooming.api.IGroomingExchangeRateServiceBase;
import com.moego.server.grooming.service.MoeGroomingExchangeRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class GroomingExchangeRateServer extends IGroomingExchangeRateServiceBase {

    @Autowired
    private MoeGroomingExchangeRateService exchangeRateService;

    @Override
    public ResponseResult requestExchangeRate() {
        return ResponseResult.success(exchangeRateService.requestExchangeRate());
    }
}
