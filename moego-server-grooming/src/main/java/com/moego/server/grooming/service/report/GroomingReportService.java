package com.moego.server.grooming.service.report;

import static com.moego.server.grooming.constant.AppointmentStatusSet.ACTIVE_STATUS_SET;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateCollectedAmount;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateTips;
import static java.math.RoundingMode.HALF_UP;

import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.AmountUtils;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.GroomingUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.customer.client.ICustomerReportClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerWithAddressAndContact;
import com.moego.server.customer.dto.GroomingCalenderPetInfo;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.ReportServiceProduct;
import com.moego.server.grooming.dto.ReportWebSale;
import com.moego.server.grooming.dto.report.CustomerSpend;
import com.moego.server.grooming.dto.report.EmployeeContribute;
import com.moego.server.grooming.dto.report.GroomingMobileOverviewDTO;
import com.moego.server.grooming.dto.report.MobileSummaryDTO;
import com.moego.server.grooming.dto.report.MobileSummaryDTO.MobileSummaryDTOBuilder;
import com.moego.server.grooming.dto.report.ReportApptsNumberDTO;
import com.moego.server.grooming.dto.report.ReportCollectedRevenueDTO;
import com.moego.server.grooming.dto.report.ReportExpectedRevenueDTO;
import com.moego.server.grooming.dto.report.ReportPaymentSummaryDto;
import com.moego.server.grooming.dto.report.ReportRecurringCustomerDTO;
import com.moego.server.grooming.dto.report.ReportTrendDTO;
import com.moego.server.grooming.dto.report.ReportTrendData;
import com.moego.server.grooming.dto.report.ReportTrendDataDTO;
import com.moego.server.grooming.dto.report.ReportTrendPeriodDTO;
import com.moego.server.grooming.dto.report.ReportWebAppointment;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.dto.report.ReportWebSaleService;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingInvoiceApplyPackageMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.params.ReportWebApptsRequest;
import com.moego.server.grooming.params.report.GetDashboardOverviewParams;
import com.moego.server.grooming.params.report.GetReportTrendParams;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.dto.GroomingReportApptDetail;
import com.moego.server.grooming.service.dto.GroomingReportApptInvoice;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportServiceDto;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.StaffPaymentForReportDTO;
import com.moego.server.grooming.service.dto.UsedPackageWithPrice;
import com.moego.server.grooming.service.dto.report.CollectedAmountCollection;
import com.moego.server.grooming.service.dto.report.CollectedPriceDTO;
import com.moego.server.grooming.service.params.CalCollectedAmountParams;
import com.moego.server.grooming.service.params.CalCollectedPriceParams;
import com.moego.server.grooming.service.utils.ReportBeanUtil;
import com.moego.server.grooming.service.utils.ReportUtil;
import com.moego.server.grooming.utils.StatusUtil;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.payment.dto.InvoicePaymentDto;
import com.moego.server.payment.dto.RefundDTO;
import com.moego.server.payment.params.QueryPaymentParams;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Report 相关代码
 */
@Slf4j
@Service
public class GroomingReportService {

    @Autowired
    private AppointmentMapperProxy appointmentMapper;

    @Autowired
    private MoeGroomingInvoiceApplyPackageMapper invoiceApplyPackageMapper;

    @Autowired
    private IPaymentPaymentClient paymentService;

    @Autowired
    private ICustomerReportClient iCustomerReportClient;

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private IPaymentRefundClient iPaymentRefundClient;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ReportOrderService reportOrderService;

    @Autowired
    private PayrollReportService payrollReportService;

    @Autowired
    private ReportAppointmentService reportAppointmentService;

    @Autowired
    private ReportCalculateService reportCalculateService;

    @Autowired
    private IPetClient iPetClient;

    public List<ReportRecurringCustomerDTO> getRecurringClientReport(
            Integer businessId, String startDate, String endDate) {
        // 获取指定时间段的所有appts
        List<GroomingReportApptDetail> apptList =
                reportAppointmentService.getReportApptByStartDateRange(businessId, startDate, endDate, false);
        reportOrderService.fillReportApptInvoiceInfo(businessId, apptList, null, true);
        // 查询出对应时间段的预约后，过滤repeat预约，找出clientId（recurring）
        Set<Integer> customerIds = apptList.stream()
                .map(GroomingReportApptDetail::getCustomerId)
                .filter(customerId -> customerId > 0)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(customerIds)) {
            return List.of();
        }
        List<Integer> recurringCustomerIds =
                appointmentMapper.getCustomerIdsWithUncancelledRepeat(businessId, customerIds);
        if (CollectionUtils.isEmpty(recurringCustomerIds)) {
            return List.of();
        }

        List<CustomerWithAddressAndContact> recurringCustomers =
                iCustomerReportClient.queryCustomerWithAddressAndContact(recurringCustomerIds);

        // refund查询
        Set<Integer> allInvoiceIds = apptList.stream()
                .map(a -> a.getInvoices().stream()
                        .map(GroomingReportApptInvoice::getInvoiceId)
                        .collect(Collectors.toSet()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(allInvoiceIds);

        List<ReportRecurringCustomerDTO> result = new ArrayList<>();
        recurringCustomers.forEach(customerDetail -> {
            ReportRecurringCustomerDTO resultDto = new ReportRecurringCustomerDTO();
            BeanUtils.copyProperties(customerDetail, resultDto);
            resultDto.setDateJoined(DateUtil.getStringDate(customerDetail.getCreateTime()));
            resultDto.setCollectedRevenue(BigDecimal.ZERO);
            resultDto.setTotalPayment(BigDecimal.ZERO);
            resultDto.setTotalRefund(BigDecimal.ZERO);
            // 根据customerId统计所有invoice的paidAmount
            // 计算lastService和nextService
            reportCalculateService.setRecurringClientSaleAndServices(
                    resultDto, apptList, customerDetail.getCustomerId(), DateUtil.getNowDateString(), refundMap);
            result.add(resultDto);
        });

        return result;
    }

    public MobileSummaryDTO getMobileDashboardSummary(Integer businessId, String startDate, String endDate) {
        MobileSummaryDTOBuilder summaryDTOBuilder = MobileSummaryDTO.initDefaultValueBuilder();
        List<GroomingReportApptDetail> appointments =
                reportCalculateService.processAppointment(businessId, startDate, endDate, summaryDTOBuilder);

        if (!appointments.isEmpty()) {
            reportCalculateService.processPetAndClientId(appointments, summaryDTOBuilder);
            reportCalculateService.processInvoice(appointments, summaryDTOBuilder);
        }
        return summaryDTOBuilder.build();
    }

    /**
     * 获取 payment method report：根据payment记录，按照每种payment method支付了多少钱返回记录
     * 1.查询appt列表
     * 2.查询payment记录
     * 3.根据payment method进行分配金额
     * 注：
     * 不带staffId筛选，获取每个appt对应的全部petDetail，统计总值
     * 带staffId筛选，在staff计算分配时，同样会计算每个staff分别统计的值，但只取该staff的数据返回
     *
     * @param businessId 商家id
     * @param startDate 查询范围 - 开始日期
     * @param endDate 查询范围 - 结束日期
     * @param staffId staffId, 不为空：只查询该staff，为空：查询所有staff
     * @return
     */
    public List<ReportPaymentSummaryDto> paymentMethodReport(
            Integer businessId, String startDate, String endDate, Integer staffId) {
        List<GroomingReportApptDetail> apptList =
                reportAppointmentService.getReportApptByStartDateRange(businessId, startDate, endDate, true);
        reportAppointmentService.fillReportApptPetDetail(businessId, apptList, false, false);
        reportOrderService.fillReportApptInvoiceInfo(businessId, apptList, null, true);

        if (CollectionUtils.isEmpty(apptList)) {
            return Collections.emptyList();
        }

        if (staffId != null) {
            // 如果有staff筛选，过滤不包含staff的appt
            apptList.removeIf(appt -> {
                for (ReportWebApptPetDetail pd : appt.getPetDetails()) {
                    if (staffId.equals(pd.getStaffId())) {
                        return false;
                    }
                }
                return true;
            });
        }

        // 处理invoice 数据
        Map<Integer, GroomingReportApptDetail> invoiceApptMap = new HashMap<>();
        Map<Integer, Integer> invoiceIdGroomingIdMap = new HashMap<>();
        Map<Integer, String> invoiceIdTypeMap = new HashMap<>(); // invoice 和 invoice type 的映射
        apptList.forEach(a -> {
            List<GroomingReportApptInvoice> invoices = a.getInvoices();
            if (!CollectionUtils.isEmpty(invoices)) {
                invoices.forEach(i -> {
                    Integer invoiceId = i.getInvoiceId();
                    invoiceIdGroomingIdMap.put(invoiceId, a.getId());
                    invoiceIdTypeMap.put(invoiceId, i.getInvoiceType());
                    invoiceApptMap.put(invoiceId, a);
                });
            }
        });
        // 查询payment记录，需要payment method, payment amount等数据
        QueryPaymentParams queryPaymentParams = new QueryPaymentParams();
        queryPaymentParams.setBusinessId(businessId);
        queryPaymentParams.setInvoiceIds(new ArrayList<>(invoiceApptMap.keySet()));
        List<InvoicePaymentDto> paymentDtoList = paymentService.getGroomingInvoicePaymentDto(queryPaymentParams);
        if (CollectionUtils.isEmpty(paymentDtoList)) {
            return Collections.emptyList();
        }

        // 过滤掉已删除并且不是 noshowfee 的 payment
        Iterator<InvoicePaymentDto> paymentIterator = paymentDtoList.iterator();
        while (paymentIterator.hasNext()) {
            InvoicePaymentDto paymentDto = paymentIterator.next();
            Integer invoiceId = paymentDto.getInvoiceId();
            GroomingReportApptDetail appt = invoiceApptMap.get(invoiceId);
            String invoiceType = invoiceIdTypeMap.get(invoiceId);
            if (AppointmentStatusEnum.CANCELED.getValue().equals(appt.getStatus())
                    && !InvoiceStatusEnum.TYPE_NOSHOW.equals(invoiceType)) {
                paymentIterator.remove();
            }
        }

        // 查询refund，由于refund没有保存refund method，所以用refund originPaymentId作映射，以originPaymentId的method作为refund method
        List<RefundDTO> refunds = iPaymentRefundClient.getRefunds(
                PaymentMethodEnum.MODULE_GROOMING, new ArrayList<>(invoiceApptMap.keySet()));
        Map<Integer, BigDecimal> refundMap = refunds.stream()
                .filter(r -> !Objects.equals(PaymentStatusEnum.FAILED, r.getStatus()))
                .collect(Collectors.toMap(RefundDTO::getOriginPaymentId, RefundDTO::getAmount, BigDecimal::add));

        // 将payment的金额和refund金额分配到staff上
        Map<String, StaffPaymentForReportDTO> staffPaymentReportMap = reportCalculateService.distributeStaffPayment(
                businessId, staffId, invoiceIdTypeMap, invoiceApptMap, paymentDtoList, refundMap);

        // 统计每个payment method 对应的ticket集合（去重，防止一个ticket中多次使用同一种类型支付时记录多次）
        Map<String, Set<Integer>> methodTicketMap = new HashMap<>();
        // 遍历支付记录
        for (InvoicePaymentDto paymentDto : paymentDtoList) {
            String method = paymentDto.getMethod();
            Integer invoiceId = paymentDto.getInvoiceId();
            /*
            payment method 对应的ticketNum
            当1张ticket用了多种支付方式，则每种支付方式都对该ticket记录一次数量
             */
            Integer groomId = invoiceIdGroomingIdMap.get(invoiceId);
            Set<Integer> grooms = methodTicketMap.get(method);
            if (grooms == null) {
                grooms = new HashSet<>();
            }
            grooms.add(groomId);
            methodTicketMap.put(method, grooms);
        }

        List<ReportPaymentSummaryDto> summaryDtos = new ArrayList<>();
        for (var entry : staffPaymentReportMap.entrySet()) {
            String method = entry.getKey();
            StaffPaymentForReportDTO dto = staffPaymentReportMap.get(method);
            Integer ticketNum = methodTicketMap.getOrDefault(method, Set.of()).size();
            summaryDtos.add(ReportBeanUtil.buildPaymentSummaryDTO(method, dto, ticketNum));
        }
        return summaryDtos;
    }

    /**
     * Sum up count of appointments by following rules:
     * unpaid: is_paid = 2 (finished appt may not fully paid)
     * unclosed: end time < current (implement by: appt date < current date) && status = 1||2 (happened and not marked
     * as finished)
     * cancel: status = 4
     * noshow: noshow = 1
     * upcoming: start time > current (implement by: appt date >= current date) && status = 1||2 (happened and not
     * marked as finished)
     * (note that: earnedRevenue and expectedRevenue are base on status = 3 or 1&2)
     * waitingList: is_waiting_list = 1
     * onlineBooking: source = 22168
     */
    public ReportApptsNumberDTO getReportApptsNumber(Integer businessId, String startDate, String endDate) {
        List<GroomingReportApptDetail> appointments =
                reportOrderService.queryBusinessApptsAndInvoiceWithDate(businessId, startDate, endDate);

        int unpaid = 0, unclosed = 0, cancelled = 0, noShow = 0, upcoming = 0, waiting = 0, onlineBooking = 0;
        String currentDate = DateUtil.getNowDateString();
        for (GroomingReportApptDetail appointment : appointments) {
            // no show的预约不统计到unpaid appt ERP-1714
            if (AppointmentStatusEnum.FINISHED.getValue().equals(appointment.getStatus())
                    && BooleanEnum.VALUE_FALSE.equals(appointment.getIsWaitingList())
                    && GroomingAppointmentEnum.NO_SHOW_FALSE.equals(appointment.getNoShow())
                    && !Objects.equals(appointment.getIsPaid(), GroomingAppointmentEnum.PAID)) {
                List<GroomingReportApptInvoice> invoices = appointment.getInvoices();
                if (!CollectionUtils.isEmpty(invoices)) {
                    for (GroomingReportApptInvoice i : invoices) {
                        if (i.getRemainAmount().compareTo(BigDecimal.ZERO) > 0
                                && InvoiceStatusEnum.TYPE_APPOINTMENT.equals(i.getInvoiceType())) {
                            unpaid++;
                        }
                    }
                }
            }
            if (currentDate.compareTo(appointment.getAppointmentDate()) > 0
                    && AppointmentStatusSet.IN_PROGRESS_STATUS_SET.contains(
                            AppointmentStatusEnum.fromValue(appointment.getStatus()))
                    && BooleanEnum.VALUE_FALSE.equals(appointment.getIsWaitingList())) {
                unclosed++;
            }
            if (AppointmentStatusEnum.CANCELED.getValue().equals(appointment.getStatus())
                    && BooleanEnum.VALUE_FALSE.equals(appointment.getIsWaitingList())) {
                cancelled++;
            }
            if (currentDate.compareTo(appointment.getAppointmentDate()) <= 0
                    && AppointmentStatusSet.IN_PROGRESS_STATUS_SET.contains(
                            AppointmentStatusEnum.fromValue(appointment.getStatus()))
                    && BooleanEnum.VALUE_FALSE.equals(appointment.getIsWaitingList())) {
                upcoming++;
            }
            if (BooleanEnum.VALUE_TRUE.equals(appointment.getIsWaitingList())) {
                waiting++;
            }
            if (BooleanEnum.VALUE_TRUE.equals(appointment.getNoShow())
                    && BooleanEnum.VALUE_FALSE.equals(appointment.getIsWaitingList())) {
                noShow++;
            }
            if (GroomingAppointmentEnum.SOURCE_OB.equals(appointment.getSource())) {
                onlineBooking++;
            }
        }

        return ReportApptsNumberDTO.builder()
                .unpaidAppts(unpaid)
                .unclosedAppts(unclosed)
                .cancelledAppts(cancelled)
                .upcomingAppts(upcoming)
                .waitingListAppts(waiting)
                .noShowAppts(noShow)
                .onlineBookingAppts(onlineBooking)
                .build();
    }

    public GroomingMobileOverviewDTO getMobileDashboardOverview(
            Integer businessId, String startDate, String endDate, List<EmployeeContribute> staffs) {
        List<GroomingReportApptDetail> appointments =
                reportOrderService.queryBusinessApptsWithDate(List.of(businessId), startDate, endDate);

        List<GroomingReportApptDetail> validAppts = appointments.stream()
                .filter(ap -> ACTIVE_STATUS_SET.contains(AppointmentStatusEnum.fromValue(ap.getStatus())))
                .toList();
        List<Integer> validApptIds =
                validAppts.stream().map(GroomingReportApptDetail::getId).collect(Collectors.toList());
        if (validApptIds.isEmpty()) {
            return GroomingMobileOverviewDTO.builder().build();
        }

        // 1. 获取用户消费信息
        List<MoeGroomingInvoice> invoices = orderService.getListByGroomingIds(businessId, validApptIds, null);

        List<CustomerSpend> customers = getTop5SpendingClients(invoices);
        reportCalculateService.fillCustomerAvatarAndName(customers);

        // 2. 获取recurring 用户比例
        /*  需要修改成1.0的计算方式，否则DM过来的没有isRecurring标志，recurring率结果为0
            1.0、2.0 实现差异：
                1.0 查询范围：日期之间内的非cancel预约
                    判断方法：非cancel的预约client内，有过非cancel repeat的client，算作recurring client
                2.0 查询范围：日期之间内的finish 预约
                    判断方法：创建过repeat的client，会被标记为recurring client，就算cancel也不会取消标记
                            dm过来的client，都没有标记
        */
        Set<Integer> customerIds =
                validAppts.stream().map(GroomingReportApptDetail::getCustomerId).collect(Collectors.toSet());
        int recurringCustomerRate = 0;
        if (!customerIds.isEmpty()) {
            //            recurringCustomerRate = iCustomerReportClient.getRecurringCustomerRateByPost(customerIds);
            int recurringCount = appointmentMapper.countCustomerIdsWithUncancelledRepeat(businessId, customerIds);
            recurringCustomerRate = recurringCount * 100 / customerIds.size();
        }

        // 3. 获取employee（staff） 收入信息
        processStaffOverviewByWebReport(staffs, businessId, startDate, endDate);

        return GroomingMobileOverviewDTO.builder()
                .topSpendingClients(customers)
                .recurringClientPercentage(recurringCustomerRate)
                .employeeOverview(staffs)
                .build();
    }

    public GroomingMobileOverviewDTO getMobileDashboardOverviewV2(GetDashboardOverviewParams params) {
        Long companyId = params.getCompanyId();
        List<Integer> businessIds =
                params.getBusinessIds().stream().map(Long::intValue).collect(Collectors.toList());
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        List<EmployeeContribute> staffs = params.getStaffs();

        List<GroomingReportApptDetail> appointments =
                reportOrderService.queryBusinessApptsWithDate(businessIds, startDate, endDate);

        List<GroomingReportApptDetail> validAppts = appointments.stream()
                .filter(ap -> ACTIVE_STATUS_SET.contains(AppointmentStatusEnum.fromValue(ap.getStatus())))
                .toList();
        if (validAppts.isEmpty()) {
            return GroomingMobileOverviewDTO.builder().build();
        }

        Map<Integer, List<GroomingReportApptDetail>> apptMap =
                validAppts.stream().collect(Collectors.groupingBy(GroomingReportApptDetail::getBusinessId));

        // 1. 获取订单信息
        List<MoeGroomingInvoice> invoices = new ArrayList<>();
        for (Map.Entry<Integer, List<GroomingReportApptDetail>> entry : apptMap.entrySet()) {
            List<Integer> validApptIds = entry.getValue().stream()
                    .map(GroomingReportApptDetail::getId)
                    .collect(Collectors.toList());
            invoices.addAll(orderService.getListByGroomingIds(entry.getKey(), validApptIds, null));
        }

        List<CustomerSpend> customers = getTop5SpendingClients(invoices);
        reportCalculateService.fillCustomerAvatarAndName(customers);

        // 2. 获取 recurring clients 比例
        Set<Integer> customerIds =
                validAppts.stream().map(GroomingReportApptDetail::getCustomerId).collect(Collectors.toSet());
        int recurringCustomerRate = 0;
        if (!customerIds.isEmpty()) {
            int recurringCount =
                    appointmentMapper.countCustomerIdsWithUncancelledRepeatByCompanyId(companyId, customerIds);
            recurringCustomerRate = recurringCount * 100 / customerIds.size();
        }

        // 3. 获取 employee 收入信息
        List<EmployeeContribute> employeeOverviews = businessIds.stream()
                .map(businessId -> CompletableFuture.supplyAsync(
                        () -> getStaffOverviewByWebReport(businessId, startDate, endDate)))
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .toList();
        ReportBeanUtil.mergeEmployeeOverviewList(staffs, employeeOverviews);

        return GroomingMobileOverviewDTO.builder()
                .topSpendingClients(customers)
                .recurringClientPercentage(recurringCustomerRate)
                .employeeOverview(staffs)
                .build();
    }

    /**
     * 根据web 端对应report 处理 staff overview信息
     *
     * @param staffs
     * @param businessId
     * @param startDate
     * @param endDate
     */
    private void processStaffOverviewByWebReport(
            List<EmployeeContribute> staffs, Integer businessId, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(staffs)) {
            return;
        }
        List<ReportWebEmployee> employeeFullReport =
                payrollReportService.getStaffPayrollSummary(businessId, startDate, endDate);
        if (CollectionUtils.isEmpty(employeeFullReport)) {
            return;
        }
        Map<Integer, EmployeeContribute> idToEmployeeMap = new HashMap<>();
        staffs.forEach(s -> idToEmployeeMap.put(s.getId(), s));
        employeeFullReport.forEach(w -> {
            EmployeeContribute e = idToEmployeeMap.get(w.getStaffId());
            if (e == null) {
                return;
            }
            e.setCollectedRevenue(w.getCollectedRevenue());
            e.setTotalTips(w.getTotalTips());
            e.setUnpaidRevenue(w.getUnpaidRevenue());
            e.setTotalAppts(w.getTotalAppts());
            e.setTotalTaxes(w.getTotalTax());
            e.setTotalPets(w.getTotalPets());
        });
    }

    private List<EmployeeContribute> getStaffOverviewByWebReport(Integer businessId, String startDate, String endDate) {
        List<EmployeeContribute> staffs = new ArrayList<>();
        List<ReportWebEmployee> employeeFullReport =
                payrollReportService.getStaffPayrollSummary(businessId, startDate, endDate);
        if (CollectionUtils.isEmpty(employeeFullReport)) {
            return staffs;
        }
        return employeeFullReport.stream()
                .map(w -> {
                    EmployeeContribute e = new EmployeeContribute();
                    e.setId(w.getStaffId());
                    e.setCollectedRevenue(w.getCollectedRevenue());
                    e.setTotalTips(w.getTotalTips());
                    e.setUnpaidRevenue(w.getUnpaidRevenue());
                    e.setTotalAppts(w.getTotalAppts());
                    e.setTotalTaxes(w.getTotalTax());
                    e.setTotalPets(w.getTotalPets());
                    return e;
                })
                .toList();
    }

    /**
     * Find top 5 spending customers from grooming invoices
     */
    private List<CustomerSpend> getTop5SpendingClients(List<MoeGroomingInvoice> invoices) {
        Map<Integer, CustomerSpend> spendMap = new HashMap<>();

        // 查询refund
        Set<Integer> invoiceIds =
                invoices.stream().map(MoeGroomingInvoice::getId).collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(invoiceIds);

        for (MoeGroomingInvoice invoice : invoices) {
            int customerId = invoice.getCustomerId();
            CustomerSpend spend = spendMap.getOrDefault(customerId, new CustomerSpend(customerId));
            // spend = paidAmount - refundAmount
            spend.setSpend(spend.getSpend()
                    .add(invoice.getPaidAmount().subtract(refundMap.getOrDefault(invoice.getId(), BigDecimal.ZERO))));
            spendMap.put(customerId, spend);
        }

        List<CustomerSpend> customerSpends = new ArrayList<>(spendMap.values());
        customerSpends.sort((a, b) -> b.getSpend().compareTo(a.getSpend()));

        List<CustomerSpend> result = new ArrayList<>();
        for (int i = 0; i < 5 && i < customerSpends.size(); i++) {
            result.add(customerSpends.get(i));
        }
        return result;
    }

    /**
     * 获取sale report
     *
     * @param businessId businessId
     * @param reportId reportId，用于区分report类型
     * @param startDate 查询开始日期
     * @param endDate 查询结束日期
     * @return
     */
    public List<ReportWebSale> queryReportWebSale(
            Integer businessId, Integer reportId, String startDate, String endDate) {
        if (reportId == 5001) {
            List<GroomingReportWebAppointment> appointments =
                    reportAppointmentService.getPayrollAppointmentDetails(businessId, startDate, endDate);
            if (CollectionUtils.isEmpty(appointments)) {
                return Collections.emptyList();
            }
            Map<Integer, GroomingReportWebAppointment> apptMap =
                    appointments.stream().collect(Collectors.toMap(GroomingReportWebAppointment::getId, appt -> appt));

            List<ReportWebEmployee> employees =
                    reportCalculateService.buildEmployeeReports(businessId, startDate, endDate, appointments);
            List<ReportWebSale> sales = employees.stream()
                    .filter(Objects::nonNull)
                    .map(e -> {
                        ReportWebSale sale = new ReportWebSale();
                        BeanUtils.copyProperties(e, sale);
                        sale.setTips(e.getTotalTips());
                        sale.setTax(e.getTotalTax());
                        sale.setPaymentStatus(GroomingUtil.getAppPaidDesc(
                                apptMap.get(e.getAptId()).getIsPaid()));
                        return sale;
                    })
                    .collect(Collectors.toList());

            reportCalculateService.postProcessMoneyFormat(sales);

            return sales;
        } else {
            List<GroomingReportWebAppointment> appointments = reportAppointmentService.getReportWebApptByStartDateRange(
                    businessId, startDate, endDate, false, true);
            if (CollectionUtils.isEmpty(appointments)) {
                return List.of();
            }
            reportOrderService.queryAppointmentWithInvoice(businessId, appointments);
            Set<Integer> invoiceIds = appointments.stream()
                    .map(GroomingReportWebAppointment::getInvoiceId)
                    .collect(Collectors.toSet());
            // 查询package使用记录
            Map<Integer, BigDecimal> invoiceIdToPackageUse = new HashMap<>();
            List<UsedPackageWithPrice> packages =
                    invoiceApplyPackageMapper.selectByInvoiceIdWithPrice(new ArrayList<>(invoiceIds));
            packages.forEach(p -> {
                BigDecimal usage = invoiceIdToPackageUse.getOrDefault(p.getInvoiceId(), BigDecimal.ZERO);
                invoiceIdToPackageUse.put(
                        p.getInvoiceId(), usage.add(p.getPrice().multiply(BigDecimal.valueOf(p.getQuantity()))));
            });
            // 查询refund
            Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(invoiceIds);
            return appointments.stream()
                    .map(a -> buildSaleReport(a, invoiceIdToPackageUse, refundMap))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 构建一个appt的sale report对象
     * <p>
     * 应收金额：invoice表的tax、tips、discount、servicePrice为应收金额
     * 实收金额：discount实收 = 应收，其它金额需要在实际收入中依次分配
     * 分配规则：
     * 实际收入 = paidAmount - refundAmount
     * 实际收入如果小于tax，则全部分配到tax；如果大于tax，再继续分配
     * 实际收入 - tax剩余金额和tips做对比，如果小于tips，则全部分配到tips；如果大于tips，则再继续分配
     * 实际收入减去分配给tax、tips，剩下的金额+discount 分配到collected service
     * 分配顺序：tax > tips > service
     *
     * @param a 查询出的预约对象
     * @param invoiceIdToPackageUse 使用package的invoice map
     * @param refundMap 退款记录map
     * @return
     */
    private ReportWebSale buildSaleReport(
            GroomingReportWebAppointment a,
            Map<Integer, BigDecimal> invoiceIdToPackageUse,
            Map<Integer, BigDecimal> refundMap) {
        ReportWebSale saleReport = new ReportWebSale();
        BeanUtils.copyProperties(a, saleReport);

        BigDecimal paidAmount = a.getPaidAmount();
        BigDecimal paidAmountExcludeConFee = AmountUtils.subtract(a.getPaidAmount(), a.getConvenienceFee());
        BigDecimal refundAmount = refundMap.getOrDefault(a.getInvoiceId(), BigDecimal.ZERO);
        // collected 金额
        if (!Objects.equals(a.getIsPaid(), GroomingAppointmentEnum.NOT_PAY)) {
            CollectedPriceDTO collectedPriceDTO =
                    reportCalculateService.calculateCollectedPrice(new CalCollectedPriceParams()
                            .setPaidAmount(paidAmountExcludeConFee)
                            .setRefundAmount(refundAmount)
                            .setExpectedServiceTax(a.getServiceTaxAmount())
                            .setExpectedProductTax(a.getProductTaxAmount())
                            .setExpectedTips(a.getTipsAmount())
                            .setServiceDiscountAmount(a.getServiceDiscountAmount())
                            .setProductDiscountAmount(a.getProductDiscountAmount())
                            .setTotalServiceSale(a.getTotalServiceSale())
                            .setTotalProductSale(a.getTotalProductSale()));
            saleReport.setCollectedRevenue(collectedPriceDTO.getCollectedRevenue());
            saleReport.setCollectedServicePrice(collectedPriceDTO.getCollectedServicePrice());
            saleReport.setCollectedTips(collectedPriceDTO.getCollectedTips());
            saleReport.setCollectedTax(AmountUtils.sum(
                    collectedPriceDTO.getCollectedServiceTax(), collectedPriceDTO.getCollectedProductTax()));
            saleReport.setDiscount(a.getDiscountAmount());
            saleReport.setNetSaleRevenue(collectedPriceDTO.getNetSaleRevenue());
        } else {
            saleReport.setCollectedRevenue(BigDecimal.ZERO);
            saleReport.setCollectedServicePrice(BigDecimal.ZERO);
            saleReport.setCollectedTips(BigDecimal.ZERO);
            saleReport.setCollectedTax(BigDecimal.ZERO);
            saleReport.setDiscount(BigDecimal.ZERO);
            saleReport.setNetSaleRevenue(BigDecimal.ZERO);
        }
        // 设置 GroomingReportWebAppointment 对象没有的字段
        saleReport.setTotalPayment(paidAmount);
        saleReport.setTotalRefund(refundAmount);
        saleReport.setApptId(a.getId());
        saleReport.setPetIds(
                ReportUtil.getReportWebApptPetIds(List.of(a)).stream().collect(Collectors.toSet()));
        saleReport.setPackageUsage(invoiceIdToPackageUse.getOrDefault(a.getInvoiceId(), BigDecimal.ZERO));
        return saleReport;
    }

    /**
     * 查询计算 sales by service report <br/>
     * see: <a href="https://moego.atlassian.net/wiki/spaces/ET/pages/*********/Sales+reports#Sales-by-services">Sales
     * by services report</a>
     */
    public List<ReportWebSaleService> queryReportWebByService(Integer businessId, String startDate, String endDate) {
        List<GroomingReportWebAppointment> appointments =
                reportAppointmentService.getReportWebApptByStartDateRange(businessId, startDate, endDate, true, false);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }
        reportOrderService.fillReportWebApptInvoiceInfo(businessId, appointments);
        // get invoice item list
        List<Integer> invoiceIdList = appointments.stream()
                .map(GroomingReportWebAppointment::getInvoiceId)
                .collect(Collectors.toList());
        List<MoeGroomingInvoiceItem> groomingInvoiceItemList =
                orderService.getInvoiceItemByInvoiceIds(businessId, invoiceIdList);

        Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemListMap =
                groomingInvoiceItemList.stream().collect(Collectors.groupingBy(MoeGroomingInvoiceItem::getInvoiceId));
        // get refund list
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(new HashSet<>(invoiceIdList));
        // split tips map
        Map<Integer, Map<Integer, BigDecimal>> tipsSplitMap =
                reportCalculateService.getTipSplitMap(businessId, appointments);

        /*
           不从配置中获取 service , 从实际使用的 service 统计数据展示
           删除和改名称都会保留历史 service
        */
        // serviceName -> ReportWebSaleService map
        Map<String, ReportWebSaleService> nameToReportMap = new HashMap<>();
        // serviceId - amountCount
        Map<Integer, BigDecimal> collectedServicePriceMap = new HashMap<>();
        Map<Integer, BigDecimal> netSaleRevenueMap = new HashMap<>();

        Set<Integer> serviceIds = new HashSet<>();
        appointments.forEach(a -> {
            List<ReportWebApptPetDetail> petDetails = a.getPetDetails();
            if (CollectionUtils.isEmpty(petDetails)) {
                return;
            }
            // 当前 order 的 tipsMap，key:petDetailId, value:tipsMap(multi staff时多个 staff - tips)
            Map<Integer, Map<Integer, BigDecimal>> petDetailTipsMap =
                    calculateTips(tipsSplitMap.getOrDefault(a.getInvoiceId(), Map.of()), petDetails);

            // 预约中可能存在相同的服务，所以先获取去重后的服务再统计对应的值
            Set<String> serviceNameSet = new HashSet<>();
            petDetails.forEach(p -> {
                String serviceName = p.getServiceName();
                serviceNameSet.add(serviceName);
                ReportWebSaleService r = nameToReportMap.computeIfAbsent(
                        serviceName,
                        // 初始化 ReportWebSaleService
                        s -> ReportWebSaleService.builder()
                                .clientNum(0)
                                .petNum(0)
                                .serviceId(p.getServiceId())
                                .serviceName(serviceName)
                                .ticketNum(0)
                                .collectedServicePrice(BigDecimal.ZERO)
                                .netSaleRevenue(BigDecimal.ZERO)
                                .totalSale(BigDecimal.ZERO)
                                .build());
                // 累计price
                r.setTotalSale(r.getTotalSale().add(p.getServicePrice()));
                r.setPetNum(r.getPetNum() + 1);

                nameToReportMap.put(serviceName, r);
                serviceIds.add(p.getServiceId());
            });
            serviceNameSet.forEach(serviceName -> {
                ReportWebSaleService r = nameToReportMap.get(serviceName);
                if (r == null) {
                    return;
                }
                r.setTicketNum(r.getTicketNum() + 1);
                r.setClientNum(r.getClientNum() + 1);
            });
            // 以下是计算实收金额的部分，如果订单未支付则跳过
            if (GroomingAppointmentEnum.NOT_PAY.equals(a.getIsPaid())) {
                return;
            }
            // 总金额、paidAmount 减去 Convenience fee 再计算
            BigDecimal totalAmountExcludeConFee = AmountUtils.subtract(a.getTotalAmount(), a.getConvenienceFee());
            BigDecimal paidAmountExcludeConFee = AmountUtils.subtract(a.getPaidAmount(), a.getConvenienceFee());
            if (!AmountUtils.isGreaterThanZero(totalAmountExcludeConFee)) {
                return;
            }
            BigDecimal refundAmount = refundMap.getOrDefault(a.getInvoiceId(), BigDecimal.ZERO);
            BigDecimal tempTotalPaidAmount = BigDecimal.ZERO;
            BigDecimal tempTotalRefundAmount = BigDecimal.ZERO;
            List<MoeGroomingInvoiceItem> invoiceItems = invoiceItemListMap.getOrDefault(a.getInvoiceId(), List.of());
            for (int i = 0; i < invoiceItems.size(); i++) {
                MoeGroomingInvoiceItem invoiceItem = invoiceItems.get(i);
                // 当前 invoice item 对应的 pet detail
                List<ReportWebApptPetDetail> invoiceItemPetDetails = petDetails.stream()
                        .filter(pd -> Objects.equals(pd.getServiceId(), invoiceItem.getServiceId())
                                && pd.getServicePrice().compareTo(invoiceItem.getServiceUnitPrice()) == 0)
                        .toList();

                // 当前 invoice item 对应的 tips 根据 split tips map 计算
                BigDecimal tipsAmount = invoiceItemPetDetails.stream()
                        .map(p -> reportCalculateService.getPetDetailTips(petDetailTipsMap, p))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal taxAmount = invoiceItem.getTaxAmount();
                BigDecimal discountAmount = invoiceItem.getDiscountAmount();
                // 当前 item 总的应收金额 = itemSalePrice(= itemPrice - discount) + tax + tips
                BigDecimal itemTotalSale = AmountUtils.sum(invoiceItem.getTotalSalePrice(), taxAmount, tipsAmount);
                BigDecimal curItemPaidAmount;
                BigDecimal curItemRefundAmount;
                if (i == invoiceItems.size() - 1) {
                    // 最后一个 item 用倒减的方式计算
                    curItemPaidAmount = AmountUtils.subtract(paidAmountExcludeConFee, tempTotalPaidAmount);
                    curItemRefundAmount = AmountUtils.subtract(refundAmount, tempTotalRefundAmount);
                } else {
                    curItemPaidAmount = paidAmountExcludeConFee
                            .multiply(itemTotalSale)
                            .divide(totalAmountExcludeConFee, 2, HALF_UP);
                    curItemRefundAmount =
                            refundAmount.multiply(itemTotalSale).divide(totalAmountExcludeConFee, 2, HALF_UP);
                    // 累加已经计算的 item 的 paidAmount 和 refundAmount
                    tempTotalPaidAmount = tempTotalPaidAmount.add(curItemPaidAmount);
                    tempTotalRefundAmount = tempTotalRefundAmount.add(curItemRefundAmount);
                }
                // 计算当前 item 的 collected price, net sale revenue
                CollectedAmountCollection serviceCollected = calculateCollectedAmount(new CalCollectedAmountParams(
                        AmountUtils.subtract(curItemPaidAmount, curItemRefundAmount),
                        taxAmount,
                        tipsAmount,
                        discountAmount));
                BigDecimal collectedServicePrice = collectedServicePriceMap
                        .getOrDefault(invoiceItem.getServiceId(), BigDecimal.ZERO)
                        .add(serviceCollected.itemPrice());
                collectedServicePriceMap.put(invoiceItem.getServiceId(), collectedServicePrice);
                BigDecimal netSaleRevenue = netSaleRevenueMap
                        .getOrDefault(invoiceItem.getServiceId(), BigDecimal.ZERO)
                        .add(serviceCollected.netSaleRevenue());
                netSaleRevenueMap.put(invoiceItem.getServiceId(), netSaleRevenue);
            }
        });

        List<ReportWebSaleService> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(nameToReportMap)) {
            // 查询并填充service category
            Map<Integer, ReportServiceDto> serviceMap =
                    groomingServiceService.queryServiceWithCategory(businessId, serviceIds);
            nameToReportMap.forEach((name, service) -> {
                if (serviceMap.containsKey(service.getServiceId())) {
                    service.setCategoryName(
                            serviceMap.get(service.getServiceId()).getCategory());
                }
                if (!collectedServicePriceMap.containsKey(service.getServiceId())) {
                    service.setCollectedServicePrice(BigDecimal.ZERO);
                    service.setNetSaleRevenue(BigDecimal.ZERO);
                } else {
                    service.setCollectedServicePrice(collectedServicePriceMap.get(service.getServiceId()));
                    service.setNetSaleRevenue(netSaleRevenueMap.get(service.getServiceId()));
                }
            });
            result.addAll(nameToReportMap.values());
        }
        result.addAll(buildEvaluationServiceReport(appointments, refundMap, invoiceItemListMap));
        return result;
    }

    public List<ReportWebSaleService> buildEvaluationServiceReport(
            List<GroomingReportWebAppointment> appointments,
            Map<Integer, BigDecimal> refundMap,
            Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemListMap) {

        // serviceName -> ReportWebSaleService map
        Map<String, ReportWebSaleService> nameToReportMap = new HashMap<>();
        // serviceId - amountCount
        Map<Integer, BigDecimal> collectedServicePriceMap = new HashMap<>();
        Map<Integer, BigDecimal> netSaleRevenueMap = new HashMap<>();

        appointments.forEach(a -> {
            // 以下是计算实收金额的部分，如果订单未支付则跳过
            if (GroomingAppointmentEnum.NOT_PAY.equals(a.getIsPaid())) {
                return;
            }
            List<EvaluationServiceDetailDTO> evaluationDetails = a.getEvaluationDetails();
            if (CollectionUtils.isEmpty(evaluationDetails)) {
                return;
            }

            // 预约中可能存在相同的服务，所以先获取去重后的服务再统计对应的值
            Set<String> serviceNameSet = new HashSet<>();
            evaluationDetails.forEach(p -> {
                String serviceName = p.getServiceName();
                serviceNameSet.add(serviceName);
                ReportWebSaleService r = nameToReportMap.computeIfAbsent(
                        serviceName,
                        // 初始化 ReportWebSaleService
                        s -> ReportWebSaleService.builder()
                                .clientNum(0)
                                .petNum(0)
                                .serviceId(p.getServiceId().intValue())
                                .serviceName(serviceName)
                                .ticketNum(0)
                                .collectedServicePrice(BigDecimal.ZERO)
                                .netSaleRevenue(BigDecimal.ZERO)
                                .totalSale(BigDecimal.ZERO)
                                .build());
                // 累计price
                r.setTotalSale(r.getTotalSale().add(p.getServicePrice()));
                r.setPetNum(r.getPetNum() + 1);

                nameToReportMap.put(serviceName, r);
            });
            serviceNameSet.forEach(serviceName -> {
                ReportWebSaleService r = nameToReportMap.get(serviceName);
                if (r == null) {
                    return;
                }
                r.setTicketNum(r.getTicketNum() + 1);
                r.setClientNum(r.getClientNum() + 1);
            });
            // 总金额、paidAmount 减去 Convenience fee 再计算
            BigDecimal totalAmountExcludeConFee = AmountUtils.subtract(a.getTotalAmount(), a.getConvenienceFee());
            BigDecimal paidAmountExcludeConFee = AmountUtils.subtract(a.getPaidAmount(), a.getConvenienceFee());
            if (!AmountUtils.isGreaterThanZero(totalAmountExcludeConFee)) {
                return;
            }
            BigDecimal refundAmount = refundMap.getOrDefault(a.getInvoiceId(), BigDecimal.ZERO);
            BigDecimal tempTotalPaidAmount = BigDecimal.ZERO;
            BigDecimal tempTotalRefundAmount = BigDecimal.ZERO;
            List<MoeGroomingInvoiceItem> invoiceItems = invoiceItemListMap.getOrDefault(a.getInvoiceId(), List.of());
            for (int i = 0; i < invoiceItems.size(); i++) {
                MoeGroomingInvoiceItem invoiceItem = invoiceItems.get(i);

                BigDecimal taxAmount = invoiceItem.getTaxAmount();
                BigDecimal discountAmount = invoiceItem.getDiscountAmount();
                // 当前 item 总的应收金额 = itemSalePrice(= itemPrice - discount) + tax + tips
                BigDecimal itemTotalSale = AmountUtils.sum(invoiceItem.getTotalSalePrice(), taxAmount, BigDecimal.ZERO);
                BigDecimal curItemPaidAmount;
                BigDecimal curItemRefundAmount;
                if (i == invoiceItems.size() - 1) {
                    // 最后一个 item 用倒减的方式计算
                    curItemPaidAmount = AmountUtils.subtract(paidAmountExcludeConFee, tempTotalPaidAmount);
                    curItemRefundAmount = AmountUtils.subtract(refundAmount, tempTotalRefundAmount);
                } else {
                    curItemPaidAmount = paidAmountExcludeConFee
                            .multiply(itemTotalSale)
                            .divide(totalAmountExcludeConFee, 2, HALF_UP);
                    curItemRefundAmount =
                            refundAmount.multiply(itemTotalSale).divide(totalAmountExcludeConFee, 2, HALF_UP);
                    // 累加已经计算的 item 的 paidAmount 和 refundAmount
                    tempTotalPaidAmount = tempTotalPaidAmount.add(curItemPaidAmount);
                    tempTotalRefundAmount = tempTotalRefundAmount.add(curItemRefundAmount);
                }
                // 计算当前 item 的 collected price, net sale revenue
                CollectedAmountCollection serviceCollected = calculateCollectedAmount(new CalCollectedAmountParams(
                        AmountUtils.subtract(curItemPaidAmount, curItemRefundAmount),
                        taxAmount,
                        BigDecimal.ZERO,
                        discountAmount));
                BigDecimal collectedServicePrice = collectedServicePriceMap
                        .getOrDefault(invoiceItem.getServiceId(), BigDecimal.ZERO)
                        .add(serviceCollected.itemPrice());
                collectedServicePriceMap.put(invoiceItem.getServiceId(), collectedServicePrice);
                BigDecimal netSaleRevenue = netSaleRevenueMap
                        .getOrDefault(invoiceItem.getServiceId(), BigDecimal.ZERO)
                        .add(serviceCollected.netSaleRevenue());
                netSaleRevenueMap.put(invoiceItem.getServiceId(), netSaleRevenue);
            }
        });
        nameToReportMap.forEach((name, service) -> {
            service.setCategoryName("");
            if (!collectedServicePriceMap.containsKey(service.getServiceId())) {
                service.setCollectedServicePrice(BigDecimal.ZERO);
                service.setNetSaleRevenue(BigDecimal.ZERO);
            } else {
                service.setCollectedServicePrice(collectedServicePriceMap.get(service.getServiceId()));
                service.setNetSaleRevenue(netSaleRevenueMap.get(service.getServiceId()));
            }
        });
        return new ArrayList<>(nameToReportMap.values());
    }

    // 这里查询了老的invoice表, 现在这个 report 没有开放使用，后续如开放需重新实现
    @Deprecated
    public List<ReportServiceProduct> queryReportWebServiceProduct(
            Integer businessId, String startDate, String endDate) {
        return Collections.emptyList();
    }

    /**
     * 获取report trend
     *
     * @param businessId
     * @param startDate
     * @param endDate
     * @return
     */
    public ReportTrendDTO getReportTrend(Integer businessId, String startDate, String endDate) {
        if (businessId == null || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "getReportTrend param is error");
        }

        // 获取上一个周期的时间
        // 计算间隔时间
        int intervalDays = DateUtil.countDaysBetween(startDate, endDate) + 1;
        String lastStartDate;
        String lastEndDate;
        try {
            lastStartDate = DateUtil.daysBeforeAfter(startDate, -intervalDays);
            lastEndDate = DateUtil.daysBeforeAfter(startDate, -1);
        } catch (ParseException e) {
            log.error("get last period date error");
            return null;
        }
        // 从上一个周期开始到当前周期结束的数据
        var reportTrendData = getReportTrendData(businessId, lastStartDate, endDate);
        Map<String, BigDecimal> collectedRevMap = reportTrendData.collectedRevMap();
        Map<String, BigDecimal> expectedRevMap = reportTrendData.expectedRevMap();
        Map<String, Integer> apptNumMap = reportTrendData.apptNumMap();
        Map<String, Set<String>> petAndGroomIdMap = reportTrendData.petAndGroomIdMap();
        Map<String, Set<Integer>> clientIdMap = reportTrendData.clientIdMap();

        // 处理返回结果，没有统计值的日期补充默认值
        List<String> currentDates = DateUtil.generateAllDatesBetween(startDate, endDate);
        List<String> lastDates = DateUtil.generateAllDatesBetween(lastStartDate, lastEndDate);

        Map<String, Object> cpCollectedRev = new HashMap<>();
        Map<String, Object> lpCollectedRev = new HashMap<>();
        Map<String, Object> cpExpectedRev = new HashMap<>();
        Map<String, Object> lpExpectedRev = new HashMap<>();
        Map<String, Object> cpApptNum = new HashMap<>();
        Map<String, Object> lpApptNum = new HashMap<>();
        Map<String, Object> cpPetNum = new HashMap<>();
        Map<String, Object> lpPetNum = new HashMap<>();
        Map<String, Object> cpClientNum = new HashMap<>();
        Map<String, Object> lpClientNum = new HashMap<>();
        currentDates.forEach(s -> {
            cpCollectedRev.put(s, collectedRevMap.getOrDefault(s, BigDecimal.ZERO));
            cpExpectedRev.put(s, expectedRevMap.getOrDefault(s, BigDecimal.ZERO));
            cpApptNum.put(s, apptNumMap.getOrDefault(s, 0));
            if (petAndGroomIdMap.containsKey(s)) {
                cpPetNum.put(s, petAndGroomIdMap.get(s).size());
            } else {
                cpPetNum.put(s, 0);
            }
            if (clientIdMap.containsKey(s)) {
                cpClientNum.put(s, clientIdMap.get(s).size());
            } else {
                cpClientNum.put(s, 0);
            }
        });
        lastDates.forEach(s -> {
            lpCollectedRev.put(s, collectedRevMap.getOrDefault(s, BigDecimal.ZERO));
            lpExpectedRev.put(s, expectedRevMap.getOrDefault(s, BigDecimal.ZERO));
            lpApptNum.put(s, apptNumMap.getOrDefault(s, 0));
            if (petAndGroomIdMap.containsKey(s)) {
                lpPetNum.put(s, petAndGroomIdMap.get(s).size());
            } else {
                lpPetNum.put(s, 0);
            }
            if (clientIdMap.containsKey(s)) {
                lpClientNum.put(s, clientIdMap.get(s).size());
            } else {
                lpClientNum.put(s, 0);
            }
        });

        return ReportTrendDTO.builder()
                .collectedRev(ReportTrendPeriodDTO.builder()
                        .currentPeriod(cpCollectedRev)
                        .lastPeriod(lpCollectedRev)
                        .build())
                .expectedRev(ReportTrendPeriodDTO.builder()
                        .currentPeriod(cpExpectedRev)
                        .lastPeriod(lpExpectedRev)
                        .build())
                .apptNum(ReportTrendPeriodDTO.builder()
                        .currentPeriod(cpApptNum)
                        .lastPeriod(lpApptNum)
                        .build())
                .petNum(ReportTrendPeriodDTO.builder()
                        .currentPeriod(cpPetNum)
                        .lastPeriod(lpPetNum)
                        .build())
                .clientNum(ReportTrendPeriodDTO.builder()
                        .currentPeriod(cpClientNum)
                        .lastPeriod(lpClientNum)
                        .build())
                .build();
    }

    public ReportTrendDataDTO getReportTrendData(GetReportTrendParams params) {
        Long companyId = params.getCompanyId();
        Long businessId = params.getBusinessId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        if (companyId == null || businessId == null || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "getReportTrend param is error");
        }
        // 获取上一个周期的时间
        // 计算间隔时间
        int intervalDays = DateUtil.countDaysBetween(startDate, endDate) + 1;
        String lastStartDate;
        String lastEndDate;
        try {
            lastStartDate = DateUtil.daysBeforeAfter(startDate, -intervalDays);
            lastEndDate = DateUtil.daysBeforeAfter(startDate, -1);
        } catch (ParseException e) {
            log.error("get last period date error");
            return null;
        }
        // TODO 多线程查询
        var lastPeriod = getReportTrendData(businessId.intValue(), lastStartDate, lastEndDate);
        var currentPeriod = getReportTrendData(businessId.intValue(), startDate, endDate);
        return new ReportTrendDataDTO().setLastPeriod(lastPeriod).setCurrentPeriod(currentPeriod);
    }

    public ReportTrendData getReportTrendData(Integer businessId, String startDate, String endDate) {
        // 查询 appt(不带状态为4以及在wait中的appt) 详情带invoice,pet_detail
        List<GroomingReportApptDetail> appts =
                reportAppointmentService.getReportApptByStartDateRange(businessId, startDate, endDate, false);
        reportAppointmentService.fillReportApptPetDetail(businessId, appts, false, false);
        reportOrderService.fillReportApptInvoiceInfo(businessId, appts, null, true);

        // 查询refund
        Set<Integer> invoiceIds = appts.stream()
                .map(a -> a.getInvoices().stream()
                        .map(GroomingReportApptInvoice::getInvoiceId)
                        .collect(Collectors.toSet()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(invoiceIds);

        // 保存已有的映射关系
        Map<String, BigDecimal> collectedRevMap = new HashMap<>();
        Map<String, BigDecimal> expectedRevMap = new HashMap<>();
        Map<String, Integer> apptNumMap = new HashMap<>();
        Map<String, Set<String>> petAndGroomIdMap = new HashMap<>(); // 根据groomId+petId去重
        Map<String, Set<Integer>> clientIdMap = new HashMap<>(); // 全量去重
        appts.forEach(a -> {
            String apptDate = a.getAppointmentDate();
            // 统计appt
            apptNumMap.put(apptDate, apptNumMap.getOrDefault(apptDate, 0) + 1);
            // 统计client
            Set<Integer> clientIds = clientIdMap.getOrDefault(apptDate, new HashSet<>());
            clientIds.add(a.getCustomerId());
            clientIdMap.put(apptDate, clientIds);

            List<GroomingReportApptInvoice> invoices = a.getInvoices();
            if (!CollectionUtils.isEmpty(invoices)) {
                BigDecimal collectedRev = collectedRevMap.getOrDefault(apptDate, BigDecimal.ZERO);
                BigDecimal expectedRev = expectedRevMap.getOrDefault(apptDate, BigDecimal.ZERO);
                for (GroomingReportApptInvoice i : invoices) {
                    BigDecimal refundAmount = refundMap.getOrDefault(i.getInvoiceId(), BigDecimal.ZERO);
                    collectedRev =
                            collectedRev.add(i.getPaidAmount().subtract(refundAmount)); // collected修正为paid - refund
                    expectedRev = expectedRev.add(i.getPaymentAmount());
                }
                collectedRevMap.put(apptDate, collectedRev);
                expectedRevMap.put(apptDate, expectedRev);
            }
            List<ReportWebApptPetDetail> petDetails = a.getPetDetails();
            Set<String> petAndGroomIds = petAndGroomIdMap.getOrDefault(apptDate, new HashSet<>());
            if (!CollectionUtils.isEmpty(petDetails)) {
                for (ReportWebApptPetDetail p : petDetails) {
                    petAndGroomIds.add(a.getId() + "|" + p.getPetId());
                }
            }
            if (!CollectionUtils.isEmpty(a.getEvaluationDetails())) {
                for (EvaluationServiceDetailDTO p : a.getEvaluationDetails()) {
                    petAndGroomIds.add(a.getId() + "|" + p.getPetId());
                }
            }
            if (!CollectionUtils.isEmpty(petAndGroomIds)) {
                petAndGroomIdMap.put(apptDate, petAndGroomIds);
            }
        });
        return new ReportTrendData(
                startDate, endDate, collectedRevMap, expectedRevMap, apptNumMap, petAndGroomIdMap, clientIdMap);
    }

    /**
     * 获取expected rev report
     *
     * @param businessId
     * @param staffId 不传查询全部staff
     * @param startDate
     * @param endDate
     * @return
     */
    public List<ReportExpectedRevenueDTO> getExpectedRevReport(
            Integer businessId, Integer staffId, String startDate, String endDate) {
        if (businessId == null || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "getExpectedRevReport param is error");
        }
        // 获取带invoice，staff 详情的预约
        List<GroomingReportWebAppointment> appointments =
                reportAppointmentService.getReportWebApptByStartDateRange(businessId, startDate, endDate, false, true);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }
        reportOrderService.queryAppointmentWithInvoice(businessId, appointments);

        // Initiate employee records
        List<ReportWebEmployee> employees = appointments.stream()
                .flatMap(a -> a.getPetDetails().stream())
                .flatMap(detail -> {
                    if (CollectionUtils.isEmpty(detail.getOperationList())) {
                        return Stream.of(detail.getStaffId());
                    }
                    return detail.getOperationList().stream().map(GroomingServiceOperationDTO::getStaffId);
                })
                .filter(sId -> sId != null && sId > 0)
                .distinct()
                .map(sId -> ReportWebEmployee.builder()
                        .staffId(sId)
                        .collectedRevenue(BigDecimal.ZERO)
                        .unpaidRevenue(BigDecimal.ZERO)
                        .revenue(BigDecimal.ZERO)
                        .totalServicesAmount(BigDecimal.ZERO)
                        .totalAddOnsAmount(BigDecimal.ZERO)
                        .totalTips(BigDecimal.ZERO)
                        .totalTax(BigDecimal.ZERO)
                        .discount(BigDecimal.ZERO)
                        .totalAppts(0)
                        .totalPets(0)
                        .build())
                .collect(Collectors.toList());
        if (staffId != null) {
            employees = employees.stream()
                    .filter(k -> staffId.equals(k.getStaffId()))
                    .collect(Collectors.toList());
        }

        // 计算金额
        reportCalculateService.processMoney(businessId, appointments, employees, true);
        // process total appts, pets and clients
        reportCalculateService.processCounts(appointments, employees);

        // 处理返回结果
        List<ReportExpectedRevenueDTO> result = new ArrayList<>();
        employees.forEach(e -> result.add(ReportExpectedRevenueDTO.builder()
                .staffId(e.getStaffId())
                // Paid Revenue($) + Unpaid Revenue($)
                .totalExpectedRevWithTipsAndTax(e.getRevenue().setScale(2, HALF_UP))
                // Total services amount($)+ Total add-ons amount($)
                .totalExpectedRev(
                        e.getTotalServicesAmount().add(e.getTotalAddOnsAmount()).setScale(2, HALF_UP))
                .totalTips(e.getTotalTips().setScale(2, HALF_UP))
                .totalTax(e.getTotalTax().setScale(2, HALF_UP))
                .totalDiscount(e.getDiscount().setScale(2, HALF_UP))
                .apptNum(e.getTotalAppts())
                .petNum(e.getTotalPets())
                .build()));

        return result;
    }

    /**
     * 获取collected rev report
     *
     * @param businessId
     * @param staffId 不传查询全部staff
     * @param startDate
     * @param endDate
     * @return
     */
    public List<ReportCollectedRevenueDTO> getCollectedRevReport(
            Integer businessId, Integer staffId, String startDate, String endDate) {
        if (businessId == null || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "getCollectedRevReport param is error");
        }
        // 获取带invoice，staff 详情的预约
        List<GroomingReportWebAppointment> appointments =
                reportAppointmentService.getReportWebApptByStartDateRange(businessId, startDate, endDate, false, true);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }
        reportOrderService.queryAppointmentWithInvoice(businessId, appointments);

        // Initiate employee records
        List<ReportWebEmployee> employees = appointments.stream()
                .flatMap(a -> a.getPetDetails().stream())
                .flatMap(detail -> {
                    if (CollectionUtils.isEmpty(detail.getOperationList())) {
                        return Stream.of(detail.getStaffId());
                    }
                    return detail.getOperationList().stream().map(GroomingServiceOperationDTO::getStaffId);
                })
                .filter(sId -> sId != null && sId > 0)
                .distinct()
                .map(sId -> ReportWebEmployee.builder()
                        .staffId(sId)
                        .collectedRevenue(BigDecimal.ZERO)
                        .unpaidRevenue(BigDecimal.ZERO)
                        .collectedTax(BigDecimal.ZERO)
                        .collectedTips(BigDecimal.ZERO)
                        .collectedDiscount(BigDecimal.ZERO)
                        .collectedRevAll(BigDecimal.ZERO)
                        .finishedApptNum(0)
                        .finishedPetNum(0)
                        .paidApptNum(0)
                        .build())
                .collect(Collectors.toList());
        if (staffId != null) {
            employees = employees.stream()
                    .filter(k -> staffId.equals(k.getStaffId()))
                    .collect(Collectors.toList());
        }

        // process total appts, pets and clients
        reportCalculateService.processCounts(appointments, employees);
        // 计算金额
        reportCalculateService.processMoney(businessId, appointments, employees, true);

        // 处理返回结果
        List<ReportCollectedRevenueDTO> result = new ArrayList<>();
        employees.forEach(e -> result.add(ReportCollectedRevenueDTO.builder()
                .staffId(e.getStaffId())
                .collectedRevWithTipsAndTax(e.getCollectedRevenue().setScale(2, HALF_UP))
                .collectedRev(e.getCollectedRevAll())
                .collectedTips(e.getCollectedTips().setScale(2, HALF_UP))
                .collectedTax(e.getCollectedTax().setScale(2, HALF_UP))
                .collectedDiscount(e.getCollectedDiscount().setScale(2, HALF_UP))
                .unpaidRev(e.getUnpaidRevenue().setScale(2, HALF_UP))
                .finishedApptNum(e.getFinishedApptNum())
                .finishedPetNum(e.getFinishedPetNum())
                .paidApptNum(e.getPaidApptNum())
                .build()));

        return result;
    }

    /**
     * 查询appt report 详情：包括全部状态的appt
     *
     * @param request
     * @return
     */
    public List<ReportWebAppointment> queryReportApptDetail(ReportWebApptsRequest request) {
        List<GroomingReportApptDetail> appointments = reportAppointmentService.getReportApptByStartDateRange(
                request.getBusinessId(), request.getStartDate(), request.getEndDate(), false);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }
        reportAppointmentService.fillReportApptPetDetail(request.getBusinessId(), appointments, true, true);
        reportAppointmentService.fillReportApptNotes(appointments);
        reportOrderService.queryGroomReportApptDetail(request.getBusinessId(), appointments);

        // 获取全部petId,通过petIds获取pet信息
        List<Integer> pIds = ReportUtil.getReportApptPetIds(appointments);
        // pId -> petInfo map
        Map<Integer, GroomingCalenderPetInfo> pIdPetInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pIds)) {
            List<GroomingCalenderPetInfo> petInfoList = iPetClient.getGroomingCalenderPetInfo(pIds);
            petInfoList.forEach(p -> pIdPetInfoMap.put(p.getPetId(), p));
        }

        Set<Integer> allInvoiceIds = appointments.stream()
                .map(a -> a.getInvoices().stream()
                        .map(GroomingReportApptInvoice::getInvoiceId)
                        .collect(Collectors.toSet()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(allInvoiceIds);
        appointments.sort(Comparator.comparing(GroomingReportApptDetail::getAppointmentDate)
                .thenComparing(GroomingReportApptDetail::getAppointmentStartTime)
                .reversed());

        return appointments.stream()
                .map(a -> {
                    List<Integer> staffIds = ReportUtil.getReportApptStaffIds(List.of(a));

                    List<String> services = new ArrayList<>();
                    // 服务
                    List<String> service = new ArrayList<>();
                    // addOns
                    List<String> addOns = new ArrayList<>();
                    // products/service-charge
                    BigDecimal totalProductPrice = BigDecimal.ZERO;
                    BigDecimal totalServiceChargePrice = BigDecimal.ZERO;
                    BigDecimal totalServiceSale = BigDecimal.ZERO;
                    BigDecimal totalProductSale = BigDecimal.ZERO;
                    BigDecimal totalServiceChargeSale = BigDecimal.ZERO;
                    for (ReportWebApptPetDetail p : a.getPetDetails()) {
                        String serviceName = p.getServiceName();
                        if (ReportUtil.isAddon(p.getServiceType())) {
                            addOns.add(serviceName);
                        } else {
                            service.add(serviceName);
                        }

                        // 设置 services
                        services.add(serviceName);
                    }
                    for (EvaluationServiceDetailDTO e : a.getEvaluationDetails()) {
                        service.add(e.getServiceName());
                        services.add(e.getServiceName());
                    }

                    String[] comment = new String[1], alertNote = new String[1];
                    a.getAppointmentNotes().forEach(n -> {
                        if (GroomingAppointmentEnum.NOTE_ALERT.equals(n.getType())) {
                            alertNote[0] = n.getNote();
                        } else if (GroomingAppointmentEnum.NOTE_COMMENT.equals(n.getType())) {
                            comment[0] = n.getNote();
                        }
                    });

                    /*
                    处理 invoice 中的金额
                     */
                    BigDecimal paymentAmount = BigDecimal.ZERO;
                    BigDecimal paidAmount = BigDecimal.ZERO;
                    BigDecimal convenienceFee = BigDecimal.ZERO;
                    BigDecimal refundAmount = BigDecimal.ZERO;
                    BigDecimal conFeeWithhold = BigDecimal.ZERO; // 有退款时扣留的 Convenience fee
                    BigDecimal tipsAmount = BigDecimal.ZERO;
                    BigDecimal taxAmount = BigDecimal.ZERO;
                    BigDecimal serviceTaxAmount = BigDecimal.ZERO;
                    BigDecimal productTaxAmount = BigDecimal.ZERO;
                    BigDecimal serviceChargeTaxAmount = BigDecimal.ZERO;
                    BigDecimal discountAmount = BigDecimal.ZERO;
                    BigDecimal serviceDiscountAmount = BigDecimal.ZERO;
                    BigDecimal productDiscountAmount = BigDecimal.ZERO;
                    BigDecimal serviceChargeDiscountAmount = BigDecimal.ZERO;
                    BigDecimal remainAmount = BigDecimal.ZERO;
                    List<Integer> productStaffIds = new ArrayList<>();
                    List<Integer> invoiceIds = new ArrayList<>();
                    List<String> serviceCharges = new ArrayList<>();
                    List<GroomingReportApptInvoice> invoices = a.getInvoices();
                    if (!CollectionUtils.isEmpty(invoices)) {
                        for (GroomingReportApptInvoice i : invoices) {
                            invoiceIds.add(i.getInvoiceId());
                            paymentAmount = AmountUtils.sum(paymentAmount, i.getPaymentAmount());
                            paidAmount = AmountUtils.sum(paidAmount, i.getPaidAmount());
                            convenienceFee = AmountUtils.sum(convenienceFee, i.getConvenienceFee());
                            refundAmount = refundMap.getOrDefault(i.getInvoiceId(), BigDecimal.ZERO);
                            tipsAmount = AmountUtils.sum(tipsAmount, i.getTipsAmount());
                            taxAmount = AmountUtils.sum(taxAmount, i.getTaxAmount());
                            serviceTaxAmount = AmountUtils.sum(serviceTaxAmount, i.getServiceTaxAmount());
                            productTaxAmount = AmountUtils.sum(productTaxAmount, i.getProductTaxAmount());
                            serviceChargeTaxAmount =
                                    AmountUtils.sum(serviceChargeTaxAmount, i.getServiceChargeTaxAmount());
                            discountAmount = AmountUtils.sum(discountAmount, i.getDiscountAmount());
                            serviceDiscountAmount =
                                    AmountUtils.sum(serviceDiscountAmount, i.getServiceDiscountAmount());
                            productDiscountAmount =
                                    AmountUtils.sum(productDiscountAmount, i.getProductDiscountAmount());
                            serviceChargeDiscountAmount =
                                    AmountUtils.sum(serviceChargeDiscountAmount, i.getServiceChargeDiscountAmount());
                            remainAmount = AmountUtils.sum(remainAmount, i.getRemainAmount());
                            totalProductPrice = AmountUtils.sum(totalProductPrice, i.getTotalProductPrice());
                            totalServiceChargePrice =
                                    AmountUtils.sum(totalServiceChargePrice, i.getTotalServiceChargePrice());
                            totalServiceSale = AmountUtils.sum(totalServiceSale, i.getTotalServiceSale());
                            totalProductSale = AmountUtils.sum(totalProductSale, i.getTotalProductSale());
                            totalServiceChargeSale =
                                    AmountUtils.sum(totalServiceChargeSale, i.getTotalServiceChargeSale());
                            if (!CollectionUtils.isEmpty(i.getSellProductStaffIds())) {
                                productStaffIds.addAll(i.getSellProductStaffIds());
                            }
                            if (!CollectionUtils.isEmpty(i.getServiceChargeList())) {
                                serviceCharges.addAll(i.getServiceChargeList());
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(productStaffIds)) {
                        staffIds.addAll(productStaffIds);
                        staffIds = staffIds.stream().distinct().collect(Collectors.toList());
                    }

                    // 当退款金额超过（paidAmount - convenience fee）时，由商家来承担这部分损失，这里记录这个损失金额
                    BigDecimal paidAmountExcludeConFee = AmountUtils.subtract(paidAmount, convenienceFee);
                    if (refundAmount.compareTo(paidAmountExcludeConFee) > 0) {
                        conFeeWithhold = AmountUtils.subtract(refundAmount, paidAmountExcludeConFee);
                    }

                    BigDecimal paymentAmountExcludeConFee = AmountUtils.subtract(paymentAmount, convenienceFee);
                    // net sale revenue = collectedServicePrice(= paidAmount - tips - tax + discount) - refund -
                    // discount
                    BigDecimal netSaleRevenue = paidAmountExcludeConFee
                            .subtract(taxAmount)
                            .subtract(tipsAmount)
                            .subtract(refundAmount);

                    ReportWebAppointment result = ReportWebAppointment.builder()
                            .bookingId(a.getId())
                            .apptDate(DateUtil.dateToBusinessFormat(a.getAppointmentDate(), request.getDateFormat()))
                            .apptTime(DateUtil.minuteToBusinessTime(
                                    a.getAppointmentStartTime(), request.getTimeFormatType()))
                            .clientId(a.getCustomerId())
                            .createDate(DateUtil.convertDateBySeconds(
                                    a.getCreateTime(), request.getTimezoneName(), request.getDateFormat()))
                            .createById(a.getCreatedById())
                            .petIds(ReportUtil.getReportApptPetIds(List.of(a)))
                            .serviceAndAddOns(services)
                            .service(service)
                            .addOns(addOns)
                            .serviceCharges(serviceCharges.stream().distinct().toList())
                            .totalServicePrice(a.getTotalServicePrice())
                            .totalAddOnsPrice(a.getTotalAddOnsPrice())
                            .totalProductPrice(totalProductPrice)
                            .totalServiceChargePrice(totalServiceChargePrice)
                            .totalPrice(paymentAmount)
                            .staffIds(staffIds)
                            .ticketComment(comment[0])
                            .alertNote(alertNote[0])
                            .status(StatusUtil.getApptStatusForReport(a.getStatus()))
                            .paymentStatus(GroomingUtil.getAppPaidDesc(a.getIsPaid()))
                            .revenue(paymentAmountExcludeConFee) // 应收收入减去 convenience fee
                            .netSaleRevenue(netSaleRevenue)
                            .tips(tipsAmount)
                            .tax(taxAmount)
                            .discount(discountAmount)
                            .invoiceIds(invoiceIds)
                            .totalPayment(paidAmount)
                            .totalRefund(refundAmount)
                            .convenienceFee(convenienceFee)
                            .conFeeWithhold(conFeeWithhold)
                            .totalUnpaid(remainAmount)
                            .petAndServices(ReportUtil.buildPetAndServiceStr(a, pIdPetInfoMap))
                            .build();
                    // unpaid status appt set collected field
                    if (!Objects.equals(GroomingAppointmentEnum.NOT_PAY, a.getIsPaid())) {
                        CollectedPriceDTO collectedPriceDTO =
                                reportCalculateService.calculateCollectedPrice(new CalCollectedPriceParams()
                                        .setPaidAmount(paidAmountExcludeConFee)
                                        .setRefundAmount(refundAmount)
                                        .setExpectedServiceTax(serviceTaxAmount)
                                        .setExpectedProductTax(productTaxAmount)
                                        .setExpectedServiceChargeTax(serviceChargeTaxAmount)
                                        .setExpectedTips(tipsAmount)
                                        .setServiceDiscountAmount(serviceDiscountAmount)
                                        .setProductDiscountAmount(productDiscountAmount)
                                        .setServiceChargeDiscountAmount(serviceChargeDiscountAmount)
                                        .setTotalServiceSale(totalServiceSale)
                                        .setTotalProductSale(totalProductSale)
                                        .setTotalServiceChargeSale(totalServiceChargeSale));
                        result.setCollectedRevenue(collectedPriceDTO.getCollectedRevenue());
                        result.setNetSaleRevenue(collectedPriceDTO.getNetSaleRevenue());
                        result.setCollectedServicePrice(collectedPriceDTO.getCollectedServicePrice());
                        result.setCollectedProductPrice(collectedPriceDTO.getCollectedProductPrice());
                        result.setCollectedServiceChargePrice(collectedPriceDTO.getCollectedServiceChargePrice());
                        result.setCollectedTips(collectedPriceDTO.getCollectedTips());
                        result.setCollectedTax(collectedPriceDTO.getTotalCollectedTax());
                    } else {
                        result.setCollectedRevenue(BigDecimal.ZERO);
                        result.setNetSaleRevenue(BigDecimal.ZERO);
                        result.setCollectedServicePrice(BigDecimal.ZERO);
                        result.setCollectedProductPrice(BigDecimal.ZERO);
                        result.setCollectedServiceChargePrice(BigDecimal.ZERO);
                        result.setCollectedTips(BigDecimal.ZERO);
                        result.setCollectedTax(BigDecimal.ZERO);
                    }
                    return result;
                })
                .toList();
    }
}
