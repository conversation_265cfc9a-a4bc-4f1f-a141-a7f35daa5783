package com.moego.server.grooming.web.vo.client;

import com.moego.server.message.dto.ArrivalWindowSettingDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/12
 */
@Data
@Accessors(chain = true)
public class BusinessInfoVO {

    @Schema(description = "Business id")
    private Integer businessId;

    @Schema(description = "Business info name")
    private String businessName;

    @Schema(description = "Address 1")
    private String address1;

    @Schema(description = "Address 2")
    private String address2;

    @Schema(description = "Address city")
    private String addressCity;

    @Schema(description = "Address state")
    private String addressState;

    @Schema(description = "Address zipcode")
    private String addressZipcode;

    @Schema(description = "Address country")
    private String addressCountry;

    @Schema(description = "Address latitude")
    private String addressLat;

    @Schema(description = "Address longitude")
    private String addressLng;

    @Schema(description = "Business info phone number")
    private String phoneNumber;

    @Schema(description = "Business owner account email")
    private String email;

    @Schema(description = "Business info logo path")
    private String avatarPath;

    @Schema(description = "Business mode, 0-Mobile, 1-Salon")
    private Byte businessMode;

    @Schema(description = "Business timezone name")
    private String timezoneName;

    @Schema(description = "Country currency symbol, $")
    private String currencySymbol;

    @Schema(description = "Country currency codes, USD")
    private String currencyCode;

    @Schema(description = "Business time format type")
    private Byte timeFormatType;

    @Schema(description = "Business time format")
    private String timeFormat;

    @Schema(description = "Business date format type")
    private Byte dateFormatType;

    @Schema(description = "Business date format")
    private String dateFormat;

    private Byte appType;

    private ArrivalWindowSettingDto mobileArrivalWindowSetting;
}
